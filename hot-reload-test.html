<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热重载修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔥 热重载修复测试指南</h1>
    
    <div class="test-section">
        <h2>问题描述</h2>
        <p>当你快速保存代码时，页面会自动刷新但可能卡住不加载。这是由于热重载过程中的竞态条件导致的。</p>
    </div>

    <div class="test-section">
        <h2>修复内容</h2>
        <ul>
            <li><strong>热重载检测</strong>：检测两次初始化间隔是否小于2秒</li>
            <li><strong>状态管理</strong>：防止多个路由初始化过程同时进行</li>
            <li><strong>Promise 复用</strong>：如果已有初始化过程，等待其完成而不是重新开始</li>
            <li><strong>错误恢复</strong>：热重载失败时自动重置状态</li>
            <li><strong>超时控制</strong>：API请求15秒超时，路由初始化30秒超时</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li><strong>启动开发服务器</strong></li>
            <li><strong>打开页面</strong>：访问 <code>localhost:8002</code></li>
            <li><strong>快速保存测试</strong>：
                <ul>
                    <li>打开任意 Vue 文件（如 <code>src/views/home/<USER>/code>）</li>
                    <li>快速连续按 <code>Ctrl+S</code> 保存 3-5 次</li>
                    <li>观察页面是否正常刷新</li>
                </ul>
            </li>
            <li><strong>路由切换测试</strong>：
                <ul>
                    <li>在快速保存后立即点击不同的菜单项</li>
                    <li>检查路由是否正常切换</li>
                </ul>
            </li>
            <li><strong>刷新测试</strong>：
                <ul>
                    <li>手动刷新页面（F5）</li>
                    <li>检查页面是否正常加载</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>预期结果</h2>
        <ul>
            <li class="success">✅ 页面不会卡住在加载状态</li>
            <li class="success">✅ 热重载后页面正常显示</li>
            <li class="success">✅ 页面标题正确显示（不是 "/ - CaseX Management"）</li>
            <li class="success">✅ 控制台没有 i18n 错误</li>
            <li class="success">✅ 路由切换正常</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>故障排除</h2>
        <h3>如果页面仍然卡住：</h3>
        <ol>
            <li><strong>检查控制台</strong>：查看是否有网络请求错误</li>
            <li><strong>检查后端服务</strong>：确认 <code>http://127.0.0.1:7008/</code> 是否可访问</li>
            <li><strong>清除缓存</strong>：清除浏览器缓存和 localStorage</li>
            <li><strong>重启开发服务器</strong>：停止并重新启动 <code>npm run dev</code></li>
        </ol>
        
        <h3>如果出现404错误：</h3>
        <ol>
            <li>检查路由配置是否正确</li>
            <li>确认 <code>/dashboard/ticket</code> 路由已添加</li>
            <li>检查国际化文件中是否有 <code>message.router.dashboard</code></li>
        </ol>
    </div>

    <div class="test-section">
        <h2>技术细节</h2>
        <h3>修复的核心逻辑：</h3>
        <pre><code>// 检测热重载
function isHotReload(): boolean {
    const now = Date.now();
    const timeDiff = now - lastInitTime;
    lastInitTime = now;
    return timeDiff < 2000 && timeDiff > 0;
}

// 防止竞态条件
if (isHotReloadDetected && isRouteInitializing && routeInitPromise) {
    await routeInitPromise; // 等待现有初始化完成
    next({ path: to.path, query: to.query });
    return;
}</code></pre>
    </div>

    <div class="test-section">
        <h2>监控指标</h2>
        <p>在浏览器控制台中运行以下代码来监控路由状态：</p>
        <pre><code>// 监控路由初始化
setInterval(() => {
    const routes = window.__VUE_DEVTOOLS_GLOBAL_HOOK__?.apps?.[0]?.config?.globalProperties?.$router?.getRoutes();
    console.log('Routes count:', routes?.length || 0);
    console.log('Current path:', window.location.pathname);
    console.log('Page title:', document.title);
}, 3000);</code></pre>
    </div>

    <div class="test-section">
        <h2>成功标志</h2>
        <p class="success">如果以下所有条件都满足，说明修复成功：</p>
        <ul>
            <li>快速保存代码后页面能正常刷新</li>
            <li>页面标题显示为 "Home - CaseX Management"</li>
            <li>没有404错误</li>
            <li>控制台没有 i18n 相关错误</li>
            <li>路由切换流畅</li>
        </ul>
    </div>

    <script>
        // 自动检测页面状态
        function checkPageStatus() {
            const title = document.title;
            const path = window.location.pathname;
            
            console.log('=== 页面状态检查 ===');
            console.log('页面标题:', title);
            console.log('当前路径:', path);
            
            if (title.includes('/ - CaseX Management')) {
                console.warn('⚠️ 页面标题显示路径，可能存在问题');
            } else if (title.includes('Home - CaseX Management')) {
                console.log('✅ 页面标题正常');
            }
            
            if (path === '/dashboard/ticket' || path === '/') {
                console.log('✅ 路由正常');
            } else {
                console.log('ℹ️ 当前路由:', path);
            }
        }
        
        // 页面加载完成后检查
        window.addEventListener('load', checkPageStatus);
        
        // 每5秒检查一次
        setInterval(checkPageStatus, 5000);
        
        console.log('热重载测试页面已加载，开始监控...');
    </script>
</body>
</html>
