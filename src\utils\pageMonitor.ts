/**
 * 页面监控工具
 * 用于检测页面状态异常并自动修复
 */

let monitorTimer: NodeJS.Timeout | null = null;
let reloadCount = 0;
const MAX_RELOAD_COUNT = 3; // 最大重载次数，防止无限循环

/**
 * 检测页面是否处于异常状态
 */
function detectPageAbnormal(): boolean {
    const title = document.title;
    const currentPath = window.location.pathname;
    
    // 检测条件1: 标题显示Home但页面显示404
    const hasHomeTitle = title.includes('Home - CaseX Management');
    const has404Content = document.body.textContent?.includes('404') || 
                         document.body.textContent?.includes('Wrong address input');
    
    // 检测条件2: 路径不匹配
    const isRootPath = currentPath === '/' || currentPath === '/dashboard';
    const isDashboardTicketPath = currentPath === '/dashboard/ticket';
    
    // 检测条件3: 页面内容异常
    const hasValidContent = document.querySelector('.layout-container') || 
                           document.querySelector('.el-container') ||
                           document.querySelector('[data-v-]'); // Vue组件标识
    
    console.log('页面状态检测:', {
        title,
        currentPath,
        hasHomeTitle,
        has404Content,
        hasValidContent: !!hasValidContent,
        reloadCount
    });
    
    // 如果标题正确但显示404，或者路径正确但没有有效内容
    if ((hasHomeTitle && has404Content) || 
        ((isRootPath || isDashboardTicketPath) && !hasValidContent && !has404Content)) {
        return true;
    }
    
    return false;
}

/**
 * 自动重载页面
 */
function autoReloadPage(): void {
    if (reloadCount >= MAX_RELOAD_COUNT) {
        console.warn('已达到最大重载次数，停止自动重载');
        stopPageMonitor();
        return;
    }
    
    reloadCount++;
    console.log(`检测到页面异常，执行第 ${reloadCount} 次自动重载...`);
    
    // 清除监控器，防止重载过程中重复触发
    stopPageMonitor();
    
    // 延迟重载，给页面一些时间完成当前操作
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

/**
 * 启动页面监控
 */
export function startPageMonitor(): void {
    // 如果已经在监控中，先停止
    if (monitorTimer) {
        stopPageMonitor();
    }
    
    console.log('启动页面状态监控...');
    
    // 延迟启动监控，给页面时间完成初始化
    setTimeout(() => {
        monitorTimer = setInterval(() => {
            try {
                if (detectPageAbnormal()) {
                    autoReloadPage();
                }
            } catch (error) {
                console.error('页面监控检测出错:', error);
            }
        }, 3000); // 每3秒检测一次
    }, 5000); // 5秒后开始监控
}

/**
 * 停止页面监控
 */
export function stopPageMonitor(): void {
    if (monitorTimer) {
        clearInterval(monitorTimer);
        monitorTimer = null;
        console.log('页面监控已停止');
    }
}

/**
 * 重置重载计数器
 */
export function resetReloadCount(): void {
    reloadCount = 0;
    console.log('重载计数器已重置');
}

/**
 * 手动触发页面检测
 */
export function manualCheck(): boolean {
    const isAbnormal = detectPageAbnormal();
    if (isAbnormal) {
        console.log('手动检测发现页面异常');
        autoReloadPage();
    } else {
        console.log('手动检测页面状态正常');
    }
    return isAbnormal;
}

// 页面可见性变化时重置计数器
document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
        resetReloadCount();
    }
});

// 页面加载完成后自动启动监控
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startPageMonitor);
} else {
    startPageMonitor();
}

// 导出到全局对象，方便调试
if (typeof window !== 'undefined') {
    (window as any).pageMonitor = {
        start: startPageMonitor,
        stop: stopPageMonitor,
        reset: resetReloadCount,
        check: manualCheck,
        getReloadCount: () => reloadCount
    };
}
