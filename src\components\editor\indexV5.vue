<template>
	<div style="border: 1px solid #ccc">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig"
			:mode="mode" />
		<Editor style="height: 300px; overflow-y: hidden" v-model="valueHtml" :defaultConfig="editorConfig" :mode="mode"
			@onCreated="handleCreated" @onChange="handleChange" />
	</div>
</template>

<script lang="ts" scoped>
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import {getCurrentInstance} from 'vue';
import { onBeforeUnmount, ref, shallowRef, onMounted, watch ,computed} from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { i18nChangeLanguage } from '@wangeditor/editor';
import { Local, Session } from '/@/utils/storage';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
export default {
	components: { Editor, Toolbar },
	props: {
		placeholder: {
			type: String,
			default: () => 'Please input',
		},
		modelValue: String,
	},
	setup(props, { emit }) {
		i18nChangeLanguage('en');
		const { proxy } = getCurrentInstance() as any;
		// 编辑器实例，必须用 shallowRef
		const editorRef = shallowRef();
		const stores = useUserInfo();
		const { userInfos } = storeToRefs(stores);
		
		// 内容 HTML
		const valueHtml = ref(props.modelValue);

		const toolbarConfig = {
			excludeKeys: ['fontFamily'],
		};

		const editorConfig = {
			placeholder: 'Please input',
			MENU_CONF: {},
		};
		const BASE_URL = computed(() => {
			const appSettings = proxy?.$appSettings;
			return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
		});
		//上传图片
		editorConfig.MENU_CONF['uploadImage'] = {
			//server: import.meta.env.VITE_API_URL + '/common/UploadFile',
			server: `${BASE_URL.value}Api/Files/UploadFile`,
			// form-data fieldName ，默认值 'wangeditor-uploaded-image'
			fieldName: 'file',
			// 单个文件的最大体积限制，默认为 2M
			maxFileSize: 5 * 1024 * 1024, // 5M
			// 最多可上传几个文件，默认为 100
			maxNumberOfFiles: 10,
			// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
			allowedFileTypes: ['image/*'],
			// 将 meta 拼接到 url 参数中，默认 false
			metaWithUrl: false,
			// 自定义增加 http  header
			headers: {
				//Authorization: 'Bearer ' + getToken(),
				Authorization: 'Bearer ' +Local.get('token'),
				userid:userInfos.value.userId
			},
			// 跨域是否传递 cookie ，默认为 false
			withCredentials: true,
			// 超时时间，默认为 10 秒
			timeout: 20 * 1000, // 20 秒
			// 自定义插入图片
			customInsert(res, insertFn) {
				let result=JSON.parse(res.data);
				//console.log("result:",result.Data);
				; -(
					// 从 res 中找到 url alt href ，然后插图图片
					
					insertFn(result.Data.url)
				)
			}
		};
		// 组件销毁时，也及时销毁编辑器
		onBeforeUnmount(() => {
			const editor = editorRef.value;
			if (editor == null) return;
			editor.destroy();
		});

		const handleCreated = (editor) => {
			editorRef.value = editor; // 记录 editor 实例，重要！
		};

		const handleChange = (editor) => {
			emit('update:modelValue', editor.getHtml());
		};

		watch(
			() => props.modelValue,
			(value) => {
				const editor = editorRef.value;
				if (value == undefined) {
					editor.clear();
					return;
				}
				valueHtml.value = value;
			}
		);

		return {
			editorRef,
			valueHtml,
			mode: 'default', // 或 'simple'
			toolbarConfig,
			editorConfig,
			handleCreated,
			handleChange,
		};
	},
};
</script>
