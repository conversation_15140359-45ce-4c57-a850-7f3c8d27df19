import { LocationQueryRaw, createRouter, createWebHashHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import pinia from '/@/stores/index';
import { storeToRefs } from 'pinia';
import { useKeepALiveNames } from '/@/stores/keepAliveNames';
import { useRoutesList } from '/@/stores/routesList';
import { useThemeConfig } from '/@/stores/themeConfig';
import { Session, Local } from '/@/utils/storage';
import { staticRoutes, notFoundAndNoPower } from '/@/router/route';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { equals } from '/@/utils';

/**
 * 1、前端控制路由时：isRequestRoutes 为 false，需要写 roles，需要走 setFilterRoute 方法。
 * 2、后端控制路由时：isRequestRoutes 为 true，不需要写 roles，不需要走 setFilterRoute 方法），
 * 相关方法已拆解到对应的 `backEnd.ts` 与 `frontEnd.ts`（他们互不影响，不需要同时改 2 个文件）。
 * 特别说明：
 * 1、前端控制：路由菜单由前端去写（无菜单管理界面，有角色管理界面），角色管理中有 roles 属性，需返回到 userInfo 中。
 * 2、后端控制：路由菜单由后端返回（有菜单管理界面、有角色管理界面）
 */

// 读取 `/src/stores/themeConfig.ts` 是否开启后端控制路由配置
const storesThemeConfig = useThemeConfig(pinia);
const { themeConfig } = storeToRefs(storesThemeConfig);
const { isRequestRoutes } = themeConfig.value;

// 修复：添加路由初始化状态管理，防止热重载时的竞态条件
let isRouteInitializing = false;
let routeInitPromise: Promise<any> | null = null;
let lastInitTime = 0;

// 修复：检测是否为热重载导致的快速刷新
function isHotReload(): boolean {
	const now = Date.now();
	const timeDiff = now - lastInitTime;
	lastInitTime = now;
	// 如果两次初始化间隔小于2秒，认为是热重载
	return timeDiff < 2000 && timeDiff > 0;
}

/**
 * 创建一个可以被 Vue 应用程序使用的路由实例
 * @method createRouter(options: RouterOptions): Router
 * @link 参考：https://next.router.vuejs.org/zh/api/#createrouter
 */
export const router = createRouter({
	history: createWebHashHistory(),
	/**
	 * 说明：
	 * 1、notFoundAndNoPower 默认添加 404、401 界面，防止一直提示 No match found for location with path 'xxx'
	 * 2、backEnd.ts(后端控制路由)、frontEnd.ts(前端控制路由) 中也需要加 notFoundAndNoPower 404、401 界面。
	 *    防止 404、401 不在 layout 布局中，不设置的话，404、401 界面将全屏显示
	 */
	routes: [...notFoundAndNoPower, ...staticRoutes],
});

/**
 * 路由多级嵌套数组处理成一维数组
 * @param arr 传入路由菜单数据数组
 * @returns 返回处理后的一维路由菜单数组
 */
export function formatFlatteningRoutes(arr: any) {
	if (arr.length <= 0) return false;
	for (let i = 0; i < arr.length; i++) {
		if (arr[i].children) {
			arr = arr.slice(0, i + 1).concat(arr[i].children, arr.slice(i + 1));
		}
	}
	return arr;
}

/**
 * 一维数组处理成多级嵌套数组（只保留二级：也就是二级以上全部处理成只有二级，keep-alive 支持二级缓存）
 * @description isKeepAlive 处理 `name` 值，进行缓存。顶级关闭，全部不缓存
 * @link 参考：https://v3.cn.vuejs.org/api/built-in-components.html#keep-alive
 * @param arr 处理后的一维路由菜单数组
 * @returns 返回将一维数组重新处理成 `定义动态路由（dynamicRoutes）` 的格式
 */
export function formatTwoStageRoutes(arr: any) {
	if (arr.length <= 0) return false;
	const newArr: any = [];
	const cacheList: Array<string> = [];
	arr.forEach((v: any) => {
		if (v.path === '/') {
			newArr.push({ component: v.component, name: v.name, path: v.path, redirect: v.redirect, meta: v.meta, children: [] });
		} else {
			// 判断是否是动态路由（xx/:id/:name），用于 tagsView 等中使用
			// 修复：https://gitee.com/lyt-top/vue-next-admin/issues/I3YX6G
			if (v.path.indexOf('/:') > -1) {
				v.meta['isDynamic'] = true;
				v.meta['isDynamicPath'] = v.path;
			}
			newArr[0].children.push({ ...v });
			// 存 name 值，keep-alive 中 include 使用，实现路由的缓存
			// 路径：/@/layout/routerView/parent.vue
			if (newArr[0].meta.isKeepAlive && v.meta.isKeepAlive) {
				cacheList.push(v.name);
				const stores = useKeepALiveNames(pinia);
				stores.setCacheKeepAlive(cacheList);
			}
		}
	});
	return newArr;
}

// 路由加载前
router.beforeEach(async (to, from, next) => {
	// await inspectVersion();

	NProgress.configure({ showSpinner: false });

	if (to.meta.title) NProgress.start();

	const token = Local.get('token');
	const needToChangePwd = Local.get('needToChangePwd');

	if (needToChangePwd && equals(to.path, '/')) {
		Local.set('needToChangePwd', false);
		Session.clear();
		Local.clearToken();
		next({ path: '/login' });
		NProgress.done();
	}
	else if (needToChangePwd && !equals(to.path, '/change')) {
		next({ path: '/change' });
		NProgress.done();
	}
	else if (to.path === '/login' && !token) {
		next();
		NProgress.done();
	} else {
		if (!token) {
			next(`/login?redirect=${to.path}&params=${JSON.stringify(to.query ? to.query : to.params)}`);
			Session.clear();
			Local.clearToken();
			NProgress.done();
		} else if (token && to.path === '/login') {
			// let path = '/';

			// let query: LocationQueryRaw = {};

			// const queryParams = to.query?.params;
			// const params = queryParams ? JSON.parse(<string>queryParams) : null;
			// const crmAutoLogin = params ? params.crmAutoLogin : null;

			// if (to.query?.redirect) {
			// 	path = <string>to.query?.redirect;

			// 	if (params && crmAutoLogin != 1) {
			// 		query = Object.keys(params).length > 0 ? params : '';
			// 	}
			// }

			// router.push({ path });

			next();

			NProgress.done();
		} else {

			const storesRoutesList = useRoutesList(pinia);
			const { routesList } = storeToRefs(storesRoutesList);

			// 修复：防止热重载时的竞态条件
			if (routesList.value.length === 0) {
				// 检测是否为热重载
				const isHotReloadDetected = isHotReload();

				// 如果是热重载且已经在初始化中，直接等待
				if (isHotReloadDetected && isRouteInitializing && routeInitPromise) {
					try {
						await routeInitPromise;
						next({ path: to.path, query: to.query });
						return;
					} catch (error) {
						console.warn('Route initialization promise failed during hot reload, retrying...');
						// 重置状态，允许重新初始化
						isRouteInitializing = false;
						routeInitPromise = null;
					}
				}

				// 如果已经在初始化中（非热重载），等待当前初始化完成
				if (isRouteInitializing && routeInitPromise) {
					try {
						await routeInitPromise;
						next({ path: to.path, query: to.query });
						return;
					} catch (error) {
						console.warn('Route initialization promise failed, retrying...');
					}
				}

				// 开始新的初始化过程
				isRouteInitializing = true;

				if (isRequestRoutes) {
					routeInitPromise = (async () => {
						try {
							// 后端控制路由：路由数据初始化，防止刷新时丢失
							await initBackEndControlRoutes();
						} catch (error) {
							console.error('Backend route initialization failed:', error);
							// 修复：后端路由初始化失败时，使用前端路由作为降级方案
							console.warn('Falling back to frontend routes');
							await initFrontEndControlRoutes();
						}
					})();

					try {
						await routeInitPromise;
						// 解决刷新时，一直跳 404 页面问题，关联问题 No match found for location with path 'xxx'
						// to.query 防止页面刷新时，普通路由带参数时，参数丢失。动态路由（xxx/:id/:name"）isDynamic 无需处理
						// 修复：防止根路由导致的无限重定向
						if (to.path === '/' && to.redirectedFrom) {
							// 如果是从根路由重定向过来的，直接放行
							next();
						} else {
							next({ path: to.path, query: to.query });
						}
					} catch (error) {
						console.error('Route initialization completely failed:', error);
						next({ path: '/login' });
					} finally {
						isRouteInitializing = false;
						routeInitPromise = null;
					}
				} else {
					routeInitPromise = initFrontEndControlRoutes();

					try {
						// https://gitee.com/lyt-top/vue-next-admin/issues/I5F1HP
						await routeInitPromise;
						// 修复：防止根路由导致的无限重定向
						if (to.path === '/' && to.redirectedFrom) {
							// 如果是从根路由重定向过来的，直接放行
							next();
						} else {
							next({ path: to.path, query: to.query });
						}
					} catch (error) {
						console.error('Frontend route initialization failed:', error);
						next({ path: '/login' });
					} finally {
						isRouteInitializing = false;
						routeInitPromise = null;
					}
				}
			} else {
				next();
			}
		}
	}
});

router.onError((error, to) => {
	if (error.message.includes('Failed to fetch dynamically imported module')) {
		alert('Failed to fetch dynamically imported module3331');
		window.location.href = to.fullPath
	}
})

// 路由加载后
router.afterEach(() => {
	NProgress.done();
});

const inspectVersion = async () => {
	// if (route.path !== '/login') {
	// @ts-ignore
	if ((Local.get('version') && Local.get('version') !== __NEXT_VERSION__) || !Local.get('version')) {
		// alert(__NEXT_VERSION__)
		Local.clearLogin();
		window.location.reload();
		Local.set('version', __NEXT_VERSION__);
	}
	// }
};

// 导出路由
export default router;
