{"name": "bpts-ticket", "version": "1.0.70", "description": "vue3 vite next admin template", "author": "lyt_20201208", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@logicflow/core": "^1.2.23", "@logicflow/extension": "^1.2.23", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.9", "@vue-office/pptx": "^0.0.6", "@vxe-ui/plugin-render-element": "^4.0.10", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.8", "countup.js": "^2.8.0", "cropperjs": "^1.6.1", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.6.1", "js-cookie": "^3.0.5", "js-table2excel": "^1.1.2", "jsplumb": "^2.15.6", "mitt": "^3.0.1", "node-forge": "^1.3.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.12.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.2", "splitpanes": "^3.1.5", "uuid": "^9.0.1", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.6", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.10.2", "vue-router": "^4.3.0", "vxe-pc-ui": "^4.3.97", "vxe-table": "^4.13.28"}, "devDependencies": {"@types/node": "^20.11.28", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-vue": "^5.2.4", "@vue/compiler-sfc": "^3.4.21", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.2.5", "sass": "^1.72.0", "typescript": "^5.4.2", "vite": "^6.3.5", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vite-plugin-web-config": "^0.0.2", "vue-eslint-parser": "^9.4.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}