<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无缝重载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .error { border-color: #dc3545; background: #f8d7da; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        
        .status {
            font-weight: bold;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>⚡ 无缝重载功能测试</h1>
    
    <div class="test-box">
        <h2>功能说明</h2>
        <p>新的无缝重载机制特点：</p>
        <ul>
            <li><strong>即时响应</strong>：每200ms检查一次标题</li>
            <li><strong>标题监控</strong>：使用MutationObserver监听标题变化</li>
            <li><strong>无延迟重载</strong>：检测到异常立即重载，无等待时间</li>
            <li><strong>精准检测</strong>：专注于标题异常，减少误判</li>
        </ul>
    </div>

    <div class="test-box">
        <h2>检测条件</h2>
        <div>
            <strong>异常标题1：</strong> <code>/ - CaseX Management</code><br>
            <strong>异常标题2：</strong> <code>/dashboard - CaseX Management</code><br>
            <strong>异常标题3：</strong> <code>Home - CaseX Management</code> + 页面显示404
        </div>
    </div>

    <div class="test-box">
        <h2>实时状态</h2>
        <div class="status" id="status">正在监控...</div>
        <div>
            <strong>当前标题：</strong> <span id="current-title">-</span><br>
            <strong>当前路径：</strong> <span id="current-path">-</span><br>
            <strong>监控状态：</strong> <span id="monitor-status">-</span>
        </div>
    </div>

    <div class="test-box">
        <h2>测试操作</h2>
        <button onclick="testAbnormalTitle1()">测试异常标题1</button>
        <button onclick="testAbnormalTitle2()">测试异常标题2</button>
        <button onclick="testHomeWith404()" class="danger">测试Home+404</button>
        <button onclick="resetTitle()">重置标题</button>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <div class="test-box">
        <h2>监控日志</h2>
        <div class="log" id="log"></div>
    </div>

    <script>
        let logCount = 0;
        
        function addLog(message) {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const log = document.getElementById('log');
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`[无缝重载测试] ${message}`);
        }
        
        function updateStatus() {
            document.getElementById('current-title').textContent = document.title;
            document.getElementById('current-path').textContent = window.location.pathname;
            document.getElementById('monitor-status').textContent = '活跃';
        }
        
        function testAbnormalTitle1() {
            addLog('测试异常标题1: / - CaseX Management');
            document.title = '/ - CaseX Management';
            updateStatus();
        }
        
        function testAbnormalTitle2() {
            addLog('测试异常标题2: /dashboard - CaseX Management');
            document.title = '/dashboard - CaseX Management';
            updateStatus();
        }
        
        function testHomeWith404() {
            addLog('测试Home标题+404内容');
            document.title = 'Home - CaseX Management';
            
            // 添加404内容
            const errorDiv = document.createElement('div');
            errorDiv.innerHTML = `
                <div style="text-align: center; padding: 50px; background: #fff;">
                    <h1 style="font-size: 72px; color: #ccc;">404</h1>
                    <p>Wrong address input, please re-enter the address~</p>
                </div>
            `;
            document.body.appendChild(errorDiv);
            updateStatus();
        }
        
        function resetTitle() {
            document.title = '无缝重载测试';
            // 移除404内容
            const errorDivs = document.querySelectorAll('div');
            errorDivs.forEach(div => {
                if (div.textContent.includes('404')) {
                    div.remove();
                }
            });
            addLog('标题已重置');
            updateStatus();
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            logCount = 0;
            addLog('日志已清除');
        }
        
        // 模拟监控脚本的检测逻辑
        function simulateMonitoring() {
            function isAbnormalTitle(title) {
                if (title.includes('/ - CaseX Management') || 
                    title.includes('/dashboard - CaseX Management')) {
                    return true;
                }
                
                if (title.includes('Home - CaseX Management')) {
                    const bodyText = document.body.textContent || '';
                    if (bodyText.includes('404') || bodyText.includes('Wrong address input')) {
                        return true;
                    }
                }
                
                return false;
            }
            
            function checkTitle() {
                const title = document.title;
                const status = document.getElementById('status');
                
                if (isAbnormalTitle(title)) {
                    status.textContent = '⚠️ 检测到异常标题！（实际环境会立即重载）';
                    status.className = 'status error';
                    addLog(`异常标题检测: ${title}`);
                } else {
                    status.textContent = '✅ 标题正常';
                    status.className = 'status success';
                }
            }
            
            // 每200ms检查一次（与实际脚本一致）
            setInterval(checkTitle, 200);
            
            // 监听标题变化
            const titleElement = document.querySelector('title');
            if (titleElement) {
                const observer = new MutationObserver(checkTitle);
                observer.observe(titleElement, { childList: true, subtree: true });
            }
        }
        
        // 初始化
        updateStatus();
        addLog('无缝重载测试页面已加载');
        simulateMonitoring();
        
        // 定期更新状态
        setInterval(updateStatus, 1000);
        
        // 页面卸载前记录
        window.addEventListener('beforeunload', function() {
            addLog('页面即将重载...');
        });
    </script>
</body>
</html>
