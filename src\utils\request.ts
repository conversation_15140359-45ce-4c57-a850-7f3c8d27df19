import axios from 'axios';
import { useUserInfo } from '/@/stores/userInfo';
import { Session, Local } from '/@/utils/storage';
import loginApi from '/@/api/login/index';
import { debugLog, isNotEmptyOrNull } from '/@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import appSettings from '/@/config/index.js';
import aTestApi from '/@/api/ATest';

import forge from 'node-forge';
function encryptData(publicKeyPem: string, data: string): string {
	const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);
	const encrypted = publicKey.encrypt(data, 'RSA-OAEP', {
		md: forge.md.sha256.create(),
	});
	return forge.util.encode64(encrypted);
}
// 生成唯一的 nonce
const generateNonce = (): string => {
	const array = new Uint8Array(16);
	window.crypto.getRandomValues(array);
	return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
};

// 配置新建一个 axios 实例
const service = axios.create({
	//baseURL: import.meta.env.VITE_API_URL as any,
	baseURL: appSettings.BASE_URL,
	timeout: appSettings.API_TIMEOUT,
	headers: { 'Content-Type': 'application/json' },
	// paramsSerializer: {
	// 	serialize(params) {
	// 		return qs.stringify(params, { allowDots: true });
	// 	},
	// },
});

// 添加请求拦截器
service.interceptors.request.use(
	(config) => {
		// 在发送请求之前做些什么 token
		if (Local.get('token')) {
			config.headers!['Authorization'] = `Bearer ${Local.get('token')}`;
		}

		if (window.location.href) {
			config.headers!['AccessUrl'] = window.location.href;
		}

		// 生成nonce和时间戳
		const nonce = generateNonce();
		const timestamp = Math.floor(new Date().getTime() / 1000); // 使用 UTC 时间
		const dataToEncrypt = `${nonce}:${timestamp}`;
		const publicKey = import.meta.env.VITE_PUBLIC_KEY;
		const encryptedData = encryptData(publicKey, dataToEncrypt);
		// 添加加密后的数据到请求头
		config.headers['Auth-Token'] = encryptedData;

		return config;
	},
	(error) => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 添加响应拦截器
service.interceptors.response.use(
	(response) => {
		const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;

		// 对响应数据做点什么
		const res = response.data;
		const { config } = response;
		debugLog('响应 ' + config.url, response);

		if (res.resultCode && res.resultCode !== 200) {
			if (res.resultCode === 4005) {
				Session.clear();
				Local.clearToken();

				// ElMessage.error('Invalid session.');

				if (isNotEmptyOrNull(webSystemUrl)) {
					window.location.href = webSystemUrl;
				} else {
					aTestApi.VueLog({ message: '4' });
					window.location.href = '/';
				}

				return;
			}

			// 无权限
			if (res.resultCode === 403) {
				// ElMessageBox.alert('You do not have permission to access this resource', 'Tip', {})
				// 	.then(() => {
				// 		// Session.clear();
				// 		// Local.clearToken();

				// 		// if (isNotEmptyOrNull(webSystemUrl)) {
				// 		// 	window.location.href = webSystemUrl;
				// 		// } else {
				// 		// 	aTestApi.VueLog({ message: '5' });
				// 		// 	window.location.href = '/';
				// 		// }
				// 	})
				// 	.catch(() => { });

				if (!window.permissionAlertManager) {
					window.permissionAlertManager = {
						instance: null,
						show() {
							// 如果已经有实例在显示，直接返回
							if (this.instance) {
								return;
							}

							// 创建新实例并保存引用
							this.instance = ElMessageBox.alert('You do not have permission to access this resource.', 'Tip', {});

							// 处理弹窗关闭后的清理
							this.instance
								.then(() => {
									// Local.clearToken();
									// if (isNotEmptyOrNull(webSystemUrl)) {
									//   window.location.href = webSystemUrl;
									// } else {
									//   aTestApi.VueLog({ message: '6' });
									//   window.location.href = '/';
									// }
								})
								.catch(() => { })
								.finally(() => {
									// 清除实例引用
									this.instance = null;
								});
						}
					};
				}

				// 调用显示方法
				window.permissionAlertManager.show();
			}

			return Promise.reject(res);
		}

		return response.data;
	},
	async (error) => {
		const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;

		if (error.response) {
			const rspData = error.response.data;

			if (rspData) {
				if (rspData.resultCode === 403) {
					// ElMessageBox.alert('You do not have permission to access this resource', 'Tip', {})
					// 	.then(() => {
					// 		// Session.clear();
					// 		// Local.clearToken();

					// 		// if (isNotEmptyOrNull(webSystemUrl)) {
					// 		// 	window.location.href = webSystemUrl;
					// 		// } else {
					// 		// 	aTestApi.VueLog({ message: '6' });
					// 		// 	window.location.href = '/';
					// 		// }
					// 	})
					// 	.catch(() => { });

					if (!window.permissionAlertManager) {
						window.permissionAlertManager = {
							instance: null,
							show() {
								// 如果已经有实例在显示，直接返回
								if (this.instance) {
									return;
								}

								// 创建新实例并保存引用
								this.instance = ElMessageBox.alert('You do not have permission to access this resource.', 'Tip', {});

								// 处理弹窗关闭后的清理
								this.instance
									.then(() => {
										// Local.clearToken();
										// if (isNotEmptyOrNull(webSystemUrl)) {
										//   window.location.href = webSystemUrl;
										// } else {
										//   aTestApi.VueLog({ message: '6' });
										//   window.location.href = '/';
										// }
									})
									.catch(() => { })
									.finally(() => {
										// 清除实例引用
										this.instance = null;
									});
							}
						};
					}

					// 调用显示方法
					window.permissionAlertManager.show();
				}
				if (rspData && rspData.resultCode === 401) {
					console.log('401-1');
					await getNewToken();
					console.log('401-2');
					error.config.headers.Authorization = `Bearer ${Local.get('token')}`;
					console.log('401-3');
					return service(error.config);
				}
			}

			return Promise.reject(error);
		}

		// 对响应错误做点什么
		if (error.message.indexOf('timeout') != -1) {
			ElMessage.error('Network Timeout');
		} else if (error.message == 'Network Error') {
			ElMessage.error(' Network connection error ');

			Session.clear();
			Local.clearToken();

			//注释掉，防止网络连接不上频繁的提示和刷新
			if (isNotEmptyOrNull(webSystemUrl)) {
				// window.location.href = webSystemUrl;
			} else {
				// aTestApi.VueLog({ message: '7' });
				// window.location.href = '/';
			}

			return;
		} else {
			if (error.response != undefined) {
				const msg = `Operation error : ${error.message}; error code: ${error.response.status}`;

				ElMessage.error(msg);

				window.console.log('error=>', msg);
			}
		}

		return Promise.reject(error);
	}
);

let promiseRT: Promise<any>;
let isRefreshing = false;

const getNewToken = () => {
	console.log('1');
	if (isRefreshing) {
		return promiseRT;
	}

	isRefreshing = true;

	const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;

	let refreshToken = Local.get('refreshToken');

	const stores = useUserInfo();

	let userName = stores.userInfos.userName;

	console.log(webSystemUrl);
	console.log('2');

	if (!refreshToken || !userName) {
		console.log('3');

		Session.clear();
		Local.clearToken();

		if (isNotEmptyOrNull(webSystemUrl)) {
			window.location.href = webSystemUrl;
		} else {
			aTestApi.VueLog({ message: '8' });
			window.location.href = '/';
		}

		return;
	}

	promiseRT = loginApi
		.RefreshToken({ token: refreshToken, userName: userName })
		.then((rs) => {
			if (!rs.data) {
				console.log('4');
				ElMessage.warning('Session has expired. Please try again.');

				Session.clear();
				Local.clearToken();

				if (isNotEmptyOrNull(webSystemUrl)) {
					window.location.href = webSystemUrl;
				} else {
					aTestApi.VueLog({ message: '9' });
					window.location.href = '/';
				}

				return;
			}
			console.log('5');
			const accessToken = rs.data.token;
			const accessTokenExp = rs.data.exp;
			const refreshToken = rs.data.refreshToken;

			Local.set('token', accessToken);
			Local.set('tokenExp', accessTokenExp);
			Local.set('refreshToken', refreshToken);
		})
		.catch((rs) => {
			console.log('6');

			Session.clear();
			Local.clearToken();

			if (isNotEmptyOrNull(webSystemUrl)) {
				window.location.href = webSystemUrl;
			} else {
				aTestApi.VueLog({ message: '10' });
				window.location.href = '/';
			}
		})
		.finally(() => {
			isRefreshing = false;
		});

	return promiseRT;
};

// 导出 axios 实例
export default service;
