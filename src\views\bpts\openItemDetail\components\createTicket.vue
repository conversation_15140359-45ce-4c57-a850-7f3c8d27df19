<template>
	<div class="system-edit-openItem-container">
		<el-dialog :title="title" v-model="state.isShowDialog" width="75vw" :close-on-click-modal="false" draggable :destroy-on-close="true" 
		@close="cancelDialog" :style="{ height: '90vh' }">
			<ticketInfo :style="{ height: 'calc(90vh - 100px)' }" :itemParams="state.params" @closeDialog="closeDialog"></ticketInfo>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { reactive, toRefs, getCurrentInstance, ref, watch, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
const ticketInfo = defineAsyncComponent(() => import('../../ticket/info.vue'));

defineProps({
  title: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['refreshData']);

const { t } = useI18n();

const { proxy } = getCurrentInstance() as any;

const state = reactive({
	loading: false,
	isShowDialog: false,
    params: {},
});

const openDialog = (params: any) => {
	state.isShowDialog = true;
    state.params = params;
	console.log(params);
};
const cancelDialog = () => {
	state.isShowDialog = false;
};

const closeDialog = () => {
	state.isShowDialog = false;
    emit('refreshData');
};

defineExpose({
	openDialog,
});

</script>

<style scoped>
</style>