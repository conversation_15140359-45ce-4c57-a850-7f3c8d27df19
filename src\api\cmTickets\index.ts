﻿import BaseApi from '/@/api/BaseApi';
import request from '/@/utils/request';

class cmTicketsApi extends BaseApi {
    TicketCount(params?: any) {
        return request({
            url: this.baseurl + 'ticketcount',
            method: 'get',
            params,
        });
    }

    TicketSave(data?: any) {
        return request({
            url: this.baseurl + 'save',
            method: 'post',
            data,
        });
    }
    UpdateTicketGrant(data?: any) {
        return request({
            url: this.baseurl + 'UpdateTicketGrant',
            method: 'post',
            data,
        });
    }
    
    //移到基类
    // Export(params?: any){
    //     return request({
    //         url: this.baseurl + 'Export',
    //         method: 'get',
    //         params,
    //         responseType: 'blob'
    //     });
    // }

    TicketDelete(data? : any){
        return request({
            url : this.baseurl + 'delete',
            method: 'post',
            data,
        })

    }
    SaveComment(data?: any) {
        return request({
            url: this.baseurl + 'SaveComment',
            method: 'post',
            data,
        });
    }
    SaveDescription(data?: any) {
        return request({
            url: this.baseurl + 'SaveDescription',
            method: 'post',
            data,
        });
    }
    
    DetailOnHold(data?: any) {
        return request({
            url: this.baseurl + 'DetailOnHold' ,
            method: 'post',
            data,
        });
    }

    QueryV2(data: any) {
        return request({
            url: this.baseurl + 'QueryV2',
            method: 'post',
            data,
        });
    }
    

}

export default new cmTicketsApi('/api/cmTickets/','id');




                        
        
        