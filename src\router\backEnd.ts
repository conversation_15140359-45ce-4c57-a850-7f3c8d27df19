import { RouteRecordRaw } from 'vue-router';
import { storeToRefs } from 'pinia';
import pinia from '/@/stores/index';
import { useUserInfo } from '/@/stores/userInfo';
import { useRequestOldRoutes } from '/@/stores/requestOldRoutes';
import { Local, Session } from '/@/utils/storage';
import { NextLoading } from '/@/utils/loading';
import { dynamicRoutes, notFoundAndNoPower } from '/@/router/route';
import { formatTwoStageRoutes, formatFlatteningRoutes, router } from '/@/router/index';
import { useRoutesList } from '/@/stores/routesList';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { useMenuApi } from '/@/api/menu/index';
import { isNotEmptyOrNull } from '/@/utils';
import aTestApi from '/@/api/ATest';

// 后端控制路由

// 引入 api 请求接口
const menuApi = useMenuApi();

/**
 * 获取目录下的 .vue、.tsx 全部文件
 * @method import.meta.glob
 * @link 参考：https://cn.vitejs.dev/guide/features.html#json
 */
const layouModules: any = import.meta.glob('../layout/routerView/*.{vue,tsx}');
const viewsModules: any = import.meta.glob('../views/**/*.{vue,tsx}');
const dynamicViewsModules: Record<string, Function> = Object.assign({}, { ...layouModules }, { ...viewsModules });

/**
 * 后端控制路由：初始化方法，防止刷新时路由丢失
 * @method NextLoading 界面 loading 动画开始执行
 * @method useUserInfo().setUserInfos() 触发初始化用户信息 pinia
 * @method useRequestOldRoutes().setRequestOldRoutes() 存储接口原始路由（未处理component），根据需求选择使用
 * @method setAddRoute 添加动态路由
 * @method setFilterMenuAndCacheTagsViewRoutes 设置路由到 pinia routesList 中（已处理成多级嵌套路由）及缓存多级嵌套数组处理后的一维数组
 */
export async function initBackEndControlRoutes() {
	const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;

	try {
		// 界面 loading 动画开始执行
		if (window.nextLoading === undefined) NextLoading.start();
		// 无 token 停止执行下一步
		if (!Local.get('token')) return false;
		// 触发初始化用户信息 pinia
		// https://gitee.com/lyt-top/vue-next-admin/issues/I5F1HP
		await useUserInfo().setUserInfos();

		var userInfo = Local.get('userInfo');

		if (!userInfo || !userInfo.userId) {
			Session.clear();
			Local.clearToken();

			if (isNotEmptyOrNull(webSystemUrl)) {
				window.location.href = webSystemUrl;
			} else {
				aTestApi.VueLog({ message: '1' });
				window.location.href = '/';
			}

			return
		}

		// 获取路由菜单数据
		const res = await getBackEndControlRoutes();

		console.log('Backend route response:', res);

		// 修复：无登录权限或路由数据为空时，使用默认路由确保系统正常运行
		// https://gitee.com/lyt-top/vue-next-admin/issues/I64HVO
		if (!res?.data || res.data.length <= 0) {
			console.warn('Backend routes empty, using default routes');
			// 使用默认的基础路由，确保至少有一个可用的路由，包括 ticket 相关路由
			const defaultRoutes = [
				{
					path: '/dashboard',
					name: 'dashboard',
					component: 'home/index',
					meta: {
						title: 'message.router.home',
						isLink: '',
						isHide: false,
						isKeepAlive: true,
						isAffix: true,
						isIframe: false,
						roles: ['admin', 'common'],
						icon: 'iconfont icon-shouye',
					},
				},
				{
					path: '/dashboard/ticket',
					name: 'ticketList',
					component: 'bpts/ticket/list',
					meta: {
						title: 'Ticket List',
						isLink: '',
						isHide: false,
						isKeepAlive: true,
						isAffix: false,
						isIframe: false,
						roles: ['admin', 'common'],
						icon: 'iconfont icon-ticket',
					},
				}
			];
			useRequestOldRoutes().setRequestOldRoutes(JSON.parse(JSON.stringify(defaultRoutes)));
			dynamicRoutes[0].children = await backEndComponent(defaultRoutes);
		} else {
			// 存储接口原始路由（未处理component），根据需求选择使用
			useRequestOldRoutes().setRequestOldRoutes(JSON.parse(JSON.stringify(res.data)));
			// 处理路由（component），替换 dynamicRoutes（/@/router/route）第一个顶级 children 的路由
			dynamicRoutes[0].children = await backEndComponent(res.data);
		}
		// 添加动态路由
		await setAddRoute();
		// 设置路由到 pinia routesList 中（已处理成多级嵌套路由）及缓存多级嵌套数组处理后的一维数组
		setFilterMenuAndCacheTagsViewRoutes();
	} catch (error: any) {
		aTestApi.VueLog({ message: error.toString().toLowerCase() });
		aTestApi.VueLog({ message: '2' });

		if (error.toString().toLowerCase().includes("request aborted")) {
			aTestApi.VueLog({ message: '211111' });
			initBackEndControlRoutes();
		} else {
			Session.clear();
			Local.clearToken();

			if (isNotEmptyOrNull(webSystemUrl)) {
				window.location.href = webSystemUrl;
			} else {
				window.location.href = '/';
			}
		}
	};
}

/**
 * 设置路由到 pinia routesList 中（已处理成多级嵌套路由）及缓存多级嵌套数组处理后的一维数组
 * @description 用于左侧菜单、横向菜单的显示
 * @description 用于 tagsView、菜单搜索中：未过滤隐藏的(isHide)
 */
export async function setFilterMenuAndCacheTagsViewRoutes() {
	const storesRoutesList = useRoutesList(pinia);
	storesRoutesList.setRoutesList(dynamicRoutes[0].children as any);
	setCacheTagsViewRoutes();
}

/**
 * 缓存多级嵌套数组处理后的一维数组
 * @description 用于 tagsView、菜单搜索中：未过滤隐藏的(isHide)
 */
export function setCacheTagsViewRoutes() {
	const storesTagsView = useTagsViewRoutes(pinia);
	storesTagsView.setTagsViewRoutes(formatTwoStageRoutes(formatFlatteningRoutes(dynamicRoutes))[0].children);
}

/**
 * 处理路由格式及添加捕获所有路由或 404 Not found 路由
 * @description 替换 dynamicRoutes（/@/router/route）第一个顶级 children 的路由
 * @returns 返回替换后的路由数组
 */
export function setFilterRouteEnd() {
	let filterRouteEnd: any = formatTwoStageRoutes(formatFlatteningRoutes(dynamicRoutes));
	// notFoundAndNoPower 防止 404、401 不在 layout 布局中，不设置的话，404、401 界面将全屏显示
	// 关联问题 No match found for location with path 'xxx'
	filterRouteEnd[0].children = [...filterRouteEnd[0].children, ...notFoundAndNoPower];
	return filterRouteEnd;
}

/**
 * 添加动态路由
 * @method router.addRoute
 * @description 此处循环为 dynamicRoutes（/@/router/route）第一个顶级 children 的路由一维数组，非多级嵌套
 * @link 参考：https://next.router.vuejs.org/zh/api/#addroute
 */
export async function setAddRoute() {
	await setFilterRouteEnd().forEach((route: RouteRecordRaw) => {
		router.addRoute(route);
	});
}

/**
 * 请求后端路由菜单接口
 * @description isRequestRoutes 为 true，则开启后端控制路由
 * @returns 返回后端路由菜单数据
 */
export function getBackEndControlRoutes() {
	// 模拟 admin 与 test
	const stores = useUserInfo(pinia);
	const { userInfos } = storeToRefs(stores);

	const webSystemUrl = import.meta.env.VITE_PUBLIC_PATH;

	try {
		const auth = userInfos.value.roles[0];
		// 管理员 admin
		if (auth === 'admin') return menuApi.getMenuAdmin();
		// 其它用户 test
		else return menuApi.getMenuTest();
	} catch (error) {
		Session.clear();
		Local.clearToken();

		if (isNotEmptyOrNull(webSystemUrl)) {
			window.location.href = webSystemUrl;
		} else {
			aTestApi.VueLog({ message: '3' });
			window.location.href = '/';
		}
	};
}

/**
 * 重新请求后端路由菜单接口
 * @description 用于菜单管理界面刷新菜单（未进行测试）
 * @description 路径：/src/views/system/menu/component/addMenu.vue
 */
export async function setBackEndControlRefreshRoutes() {
	await getBackEndControlRoutes();
}

/**
 * 后端路由 component 转换
 * @param routes 后端返回的路由表数组
 * @returns 返回处理成函数后的 component
 */
export function backEndComponent(routes: any) {
	if (!routes) return;

	return routes.map((item: any) => {
		const routComponent = item.component as string;

		if (item.component) {
			item.component = dynamicImport(dynamicViewsModules, routComponent)
		};

		// 修复：确保每个路由都有有效的meta.title，防止i18n错误
		if (item.meta && (!item.meta.title || typeof item.meta.title !== 'string' || item.meta.title.trim() === '')) {
			console.warn('Route missing valid title:', item.path, item.name);
			item.meta.title = item.name || item.path || 'Unknown';
		}

		item.children && backEndComponent(item.children);

		return item;
	});
}

/**
 * 后端路由 component 转换函数
 * @param dynamicViewsModules 获取目录下的 .vue、.tsx 全部文件
 * @param component 当前要处理项 component
 * @returns 返回处理成函数后的 component
 */
export function dynamicImport(dynamicViewsModules: Record<string, Function>, component: string) {
	const keys = Object.keys(dynamicViewsModules);

	// 调试信息：打印所有可用的组件路径
	console.log('Available component keys:', keys);
	console.log('Looking for component:', component);

	const matchKeys = keys.filter((key) => {
		const k = key.replace(/..\/views|../, '');
		const isMatch = k.startsWith(`${component}`) || k.startsWith(`/${component}`) || k.includes(component);
		console.log(`Checking key: ${key} -> ${k}, matches: ${isMatch}`);
		return isMatch;
	});

	console.log('Matched keys:', matchKeys);

	if (matchKeys?.length === 1) {
		const matchKey = matchKeys[0];
		console.log('Using matched key:', matchKey);
		return dynamicViewsModules[matchKey];
	}
	if (matchKeys?.length > 1) {
		console.warn('Multiple matches found for component:', component, matchKeys);
		// 选择最精确的匹配
		const exactMatch = matchKeys.find(key => key.endsWith(`${component}.vue`));
		if (exactMatch) {
			console.log('Using exact match:', exactMatch);
			return dynamicViewsModules[exactMatch];
		}
		return dynamicViewsModules[matchKeys[0]];
	}

	console.error('No component found for:', component);
	return undefined;
}
