<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后端路由修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .fix-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-info { background: #17a2b8; }
        
        ul li {
            margin: 8px 0;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        
        .after {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>🔧 后端路由系统修复报告</h1>
    
    <div class="fix-section success">
        <h2>问题根源发现</h2>
        <p>经过深入分析，发现页面标题异常的真正根源是<strong>后端路由系统的初始化问题</strong>：</p>
        <ul>
            <li><span class="status-indicator status-error"></span><strong>系统使用后端控制路由</strong>：<code>isRequestRoutes: true</code></li>
            <li><span class="status-indicator status-error"></span><strong>路由API延迟或失败</strong>：导致路由未完全初始化</li>
            <li><span class="status-indicator status-error"></span><strong>标题设置时机问题</strong>：在路由未匹配时就尝试设置标题</li>
            <li><span class="status-indicator status-error"></span><strong>空路由数据处理不当</strong>：后端返回空数据时直接返回，未设置默认路由</li>
        </ul>
    </div>

    <div class="fix-section info">
        <h2>修复方案详解</h2>
        
        <h3>1. 改进标题设置时机</h3>
        <div class="comparison">
            <div class="before">
                <h4>❌ 修复前</h4>
                <div class="code-block">
// 立即设置标题，可能路由还未初始化
nextTick(() => {
    const currentRoute = router.currentRoute.value;
    // 路由可能还是空的或不完整的
    webTitle = setTagsViewNameI18n(currentRoute);
});
</div>
            </div>
            <div class="after">
                <h4>✅ 修复后</h4>
                <div class="code-block">
// 延迟设置标题，确保路由完全初始化
nextTick(() => {
    setTimeout(() => {
        const currentRoute = router.currentRoute.value;
        try {
            webTitle = setTagsViewNameI18n(currentRoute);
        } catch (error) {
            webTitle = ''; // 安全降级
        }
    }, 50);
});
</div>
            </div>
        </div>
        
        <h3>2. 改进后端路由空数据处理</h3>
        <div class="comparison">
            <div class="before">
                <h4>❌ 修复前</h4>
                <div class="code-block">
// 后端返回空数据时直接返回，未设置任何路由
if (res?.data.length <= 0) {
    return Promise.resolve(true);
}
// 导致页面无可用路由
</div>
            </div>
            <div class="after">
                <h4>✅ 修复后</h4>
                <div class="code-block">
// 后端返回空数据时，设置默认路由确保系统正常运行
if (!res?.data || res.data.length <= 0) {
    const defaultRoutes = [{
        path: '/dashboard',
        name: 'dashboard',
        component: '/views/home/<USER>',
        meta: { title: 'message.router.home', ... }
    }];
    dynamicRoutes[0].children = await backEndComponent(defaultRoutes);
}
</div>
            </div>
        </div>
        
        <h3>3. 增强标题降级机制</h3>
        <div class="comparison">
            <div class="before">
                <h4>❌ 修复前</h4>
                <div class="code-block">
// 直接使用路径作为标题
tagsViewName = item.name || item.path || 'Unknown';
// 导致显示 "/ - CaseX Management"
</div>
            </div>
            <div class="after">
                <h4>✅ 修复后</h4>
                <div class="code-block">
// 根据路径智能提供有意义的标题
if (path === '/' || path === '/dashboard') {
    webTitle = 'Home';
} else if (path.includes('/ticket')) {
    webTitle = 'Ticket';
} else if (path.includes('/dashboard')) {
    webTitle = 'Dashboard';
} else {
    webTitle = 'Page';
}
</div>
            </div>
        </div>
    </div>

    <div class="fix-section">
        <h2>修复的核心文件</h2>
        
        <h3>1. src/utils/other.ts - useTitle() 函数</h3>
        <div class="code-block">
export function useTitle() {
    const stores = useThemeConfig(pinia);
    const { themeConfig } = storeToRefs(stores);
    
    // ✅ 使用 nextTick 和 setTimeout 的组合确保路由完全初始化
    nextTick(() => {
        setTimeout(() => {
            let webTitle = '';
            let globalTitle: string = themeConfig.value.globalTitle;
            const currentRoute = router.currentRoute.value;
            const { path, meta } = currentRoute;
            
            if (!path || path === '') {
                return; // ✅ 安全检查
            }
            
            if (path === '/login') {
                webTitle = meta?.title || 'Login';
            } else {
                try {
                    webTitle = setTagsViewNameI18n(currentRoute);
                } catch (error) {
                    webTitle = ''; // ✅ 错误处理
                }
            }
            
            // ✅ 智能默认标题
            if (!webTitle || webTitle.trim() === '') {
                if (path === '/' || path === '/dashboard') {
                    webTitle = 'Home';
                } else if (path.includes('/ticket')) {
                    webTitle = 'Ticket';
                } else if (path.includes('/dashboard')) {
                    webTitle = 'Dashboard';
                } else {
                    webTitle = 'Page';
                }
            }
            
            document.title = `${webTitle} - ${globalTitle}`;
        }, 50);
    });
}
</div>
        
        <h3>2. src/router/backEnd.ts - 后端路由初始化</h3>
        <div class="code-block">
// ✅ 改进的后端路由初始化逻辑
const res = await getBackEndControlRoutes();

if (!res?.data || res.data.length <= 0) {
    // ✅ 使用默认的基础路由，确保至少有一个可用的路由
    const defaultRoutes = [
        {
            path: '/dashboard',
            name: 'dashboard',
            component: '/views/home/<USER>',
            meta: {
                title: 'message.router.home',
                isLink: '',
                isHide: false,
                isKeepAlive: true,
                isAffix: true,
                isIframe: false,
                roles: ['admin', 'common'],
                icon: 'iconfont icon-shouye',
            },
        }
    ];
    useRequestOldRoutes().setRequestOldRoutes(JSON.parse(JSON.stringify(defaultRoutes)));
    dynamicRoutes[0].children = await backEndComponent(defaultRoutes);
} else {
    // 正常处理后端返回的路由数据
    useRequestOldRoutes().setRequestOldRoutes(JSON.parse(JSON.stringify(res.data)));
    dynamicRoutes[0].children = await backEndComponent(res.data);
}
</div>
    </div>

    <div class="fix-section success">
        <h2>修复效果</h2>
        <ul>
            <li><span class="status-indicator status-success"></span><strong>解决路由初始化问题</strong>：即使后端路由API失败，也有默认路由可用</li>
            <li><span class="status-indicator status-success"></span><strong>标题设置稳定</strong>：延迟设置确保路由完全初始化后再设置标题</li>
            <li><span class="status-indicator status-success"></span><strong>智能降级机制</strong>：根据路径提供有意义的默认标题</li>
            <li><span class="status-indicator status-success"></span><strong>错误处理完善</strong>：添加 try-catch 确保不会因为错误导致页面卡住</li>
            <li><span class="status-indicator status-success"></span><strong>用户体验提升</strong>：页面不再出现异常标题或卡住的情况</li>
        </ul>
    </div>

    <div class="fix-section warning">
        <h2>测试步骤</h2>
        <ol>
            <li><strong>清除缓存</strong>：清除浏览器缓存和 localStorage</li>
            <li><strong>重启服务</strong>：重启开发服务器 <code>npm run dev</code></li>
            <li><strong>访问根路径</strong>：在浏览器中访问 <code>localhost:8002</code></li>
            <li><strong>检查标题</strong>：页面标题应该显示正确的标题，不再是路径</li>
            <li><strong>检查控制台</strong>：不应该再出现 "Route not fully matched yet" 警告</li>
            <li><strong>热重载测试</strong>：快速保存代码文件，观察页面是否正常</li>
            <li><strong>网络测试</strong>：模拟网络延迟或API失败，观察是否有默认路由</li>
        </ol>
    </div>

    <div class="fix-section info">
        <h2>技术要点</h2>
        <ul>
            <li><strong>后端路由系统</strong>：理解系统使用动态路由加载机制</li>
            <li><strong>异步初始化</strong>：处理路由异步加载过程中的时机问题</li>
            <li><strong>降级策略</strong>：确保在各种异常情况下系统都能正常运行</li>
            <li><strong>错误处理</strong>：添加完善的错误处理机制</li>
            <li><strong>用户体验</strong>：确保页面加载过程对用户透明</li>
        </ul>
    </div>

    <div class="fix-section success">
        <h2>预期结果</h2>
        <p>经过这次根源修复后，应该实现以下效果：</p>
        <ul>
            <li><span class="status-indicator status-success"></span>页面标题始终正确显示，不再出现路径作为标题</li>
            <li><span class="status-indicator status-success"></span>不再出现 "Route not fully matched yet" 警告</li>
            <li><span class="status-indicator status-success"></span>页面不会卡住或无响应</li>
            <li><span class="status-indicator status-success"></span>热重载正常工作，不会导致页面异常</li>
            <li><span class="status-indicator status-success"></span>即使网络问题导致路由API失败，也有默认路由可用</li>
            <li><span class="status-indicator status-success"></span>系统整体稳定性和可靠性提升</li>
        </ul>
    </div>

    <script>
        // 页面加载完成后显示当前状态
        window.addEventListener('load', function() {
            console.log('=== 后端路由修复测试页面已加载 ===');
            console.log('当前页面标题:', document.title);
            console.log('当前URL:', window.location.href);
            console.log('请按照测试步骤验证修复效果');
            console.log('重点关注：');
            console.log('1. 页面标题是否正确显示');
            console.log('2. 控制台是否还有路由警告');
            console.log('3. 页面是否会卡住');
        });
    </script>
</body>
</html>
