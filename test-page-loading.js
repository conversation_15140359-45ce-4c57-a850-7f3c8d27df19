// 页面加载测试脚本
// 在浏览器控制台中运行此脚本来测试修复效果

console.log('开始测试页面加载修复效果...');

// 测试1: 检查页面标题
function testPageTitle() {
    console.log('测试1: 检查页面标题');
    const title = document.title;
    console.log('当前页面标题:', title);
    
    if (title.includes('/ - CaseX Management')) {
        console.error('❌ 页面标题仍然显示路径，修复可能未生效');
        return false;
    } else if (title.includes('Home - CaseX Management')) {
        console.log('✅ 页面标题正常显示');
        return true;
    } else {
        console.log('ℹ️ 页面标题:', title);
        return true;
    }
}

// 测试2: 检查网络请求状态
function testNetworkRequests() {
    console.log('测试2: 检查网络请求状态');
    
    // 检查是否有长时间pending的请求
    const performanceEntries = performance.getEntriesByType('navigation');
    if (performanceEntries.length > 0) {
        const loadTime = performanceEntries[0].loadEventEnd - performanceEntries[0].loadEventStart;
        console.log('页面加载时间:', loadTime + 'ms');
        
        if (loadTime > 30000) {
            console.warn('⚠️ 页面加载时间过长，可能存在网络请求问题');
            return false;
        } else {
            console.log('✅ 页面加载时间正常');
            return true;
        }
    }
    return true;
}

// 测试3: 检查控制台错误
function testConsoleErrors() {
    console.log('测试3: 检查控制台错误');
    
    // 监听新的错误
    let errorCount = 0;
    const originalError = console.error;
    console.error = function(...args) {
        if (args.some(arg => typeof arg === 'string' && arg.includes('Invalid arguments'))) {
            errorCount++;
            console.log('❌ 发现 i18n Invalid arguments 错误');
        }
        originalError.apply(console, args);
    };
    
    setTimeout(() => {
        console.error = originalError;
        if (errorCount === 0) {
            console.log('✅ 未发现 i18n 错误');
        } else {
            console.log(`❌ 发现 ${errorCount} 个 i18n 错误`);
        }
    }, 5000);
}

// 测试4: 模拟路由切换
function testRouteNavigation() {
    console.log('测试4: 模拟路由切换');
    
    if (window.location.pathname === '/') {
        console.log('当前在根路由，测试重定向...');
        // 检查是否正确重定向到 /dashboard/ticket
        setTimeout(() => {
            if (window.location.pathname.includes('/dashboard/ticket')) {
                console.log('✅ 根路由重定向正常');
            } else {
                console.warn('⚠️ 根路由重定向可能有问题');
            }
        }, 2000);
    }
}

// 测试5: 检查路由数据
function testRouteData() {
    console.log('测试5: 检查路由数据');
    
    // 检查 Vue Router 实例
    if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__ && window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps) {
        const app = window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps[0];
        if (app && app.config && app.config.globalProperties && app.config.globalProperties.$router) {
            const router = app.config.globalProperties.$router;
            const routes = router.getRoutes();
            console.log('路由数量:', routes.length);
            
            if (routes.length > 0) {
                console.log('✅ 路由数据加载正常');
                
                // 检查根路由是否有title
                const rootRoute = routes.find(r => r.path === '/');
                if (rootRoute && rootRoute.meta && rootRoute.meta.title) {
                    console.log('✅ 根路由有title:', rootRoute.meta.title);
                } else {
                    console.warn('⚠️ 根路由可能缺少title');
                }
            } else {
                console.warn('⚠️ 路由数据可能未正确加载');
            }
        }
    }
}

// 运行所有测试
function runAllTests() {
    console.log('='.repeat(50));
    console.log('开始运行页面加载修复测试');
    console.log('='.repeat(50));
    
    const results = [];
    
    results.push(testPageTitle());
    results.push(testNetworkRequests());
    testConsoleErrors(); // 异步测试
    testRouteNavigation(); // 异步测试
    testRouteData();
    
    const passedTests = results.filter(r => r === true).length;
    const totalTests = results.length;
    
    console.log('='.repeat(50));
    console.log(`测试完成: ${passedTests}/${totalTests} 项测试通过`);
    console.log('='.repeat(50));
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过，修复效果良好！');
    } else {
        console.log('⚠️ 部分测试未通过，可能需要进一步检查');
    }
}

// 自动运行测试
runAllTests();

// 导出测试函数供手动调用
window.pageLoadingTest = {
    runAllTests,
    testPageTitle,
    testNetworkRequests,
    testConsoleErrors,
    testRouteNavigation,
    testRouteData
};

console.log('测试脚本已加载，可以通过 window.pageLoadingTest 访问测试函数');
