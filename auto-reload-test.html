<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动重载功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-info { background: #17a2b8; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .monitor-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔄 自动重载功能测试</h1>
    
    <div class="test-section info">
        <h2>功能说明</h2>
        <p>这个功能会自动检测页面异常状态，当检测到以下情况时会自动重新加载页面：</p>
        <ul>
            <li><strong>标题异常</strong>：页面标题显示 "Home - CaseX Management" 但页面内容显示404错误</li>
            <li><strong>内容异常</strong>：在正确路径（/、/dashboard、/dashboard/ticket）但没有Vue应用内容</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>检测逻辑</h2>
        <div class="code-block">
            <strong>检测条件1：</strong> 标题包含 "Home - CaseX Management" 且页面包含 "404" 或 "Wrong address input"<br>
            <strong>检测条件2：</strong> 路径正确但没有Vue组件内容（检查5次后触发）<br>
            <strong>检测频率：</strong> 每3秒检查一次，最多检查8次<br>
            <strong>启动延迟：</strong> 页面加载完成后5秒开始检查
        </div>
    </div>

    <div class="test-section">
        <h2>实时监控</h2>
        <p>当前页面状态：</p>
        <div id="status-display">
            <p><span class="status-indicator status-info"></span>正在初始化...</p>
        </div>
        
        <button onclick="manualCheck()">手动检查</button>
        <button onclick="simulateError()">模拟404错误</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div class="monitor-output" id="log-output">
            等待检测开始...
        </div>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li><strong>正常测试</strong>：
                <ul>
                    <li>访问 <code>localhost:8002</code></li>
                    <li>观察控制台输出，应该显示页面正常</li>
                </ul>
            </li>
            <li><strong>异常模拟</strong>：
                <ul>
                    <li>点击"模拟404错误"按钮</li>
                    <li>观察页面是否在几秒后自动重载</li>
                </ul>
            </li>
            <li><strong>热重载测试</strong>：
                <ul>
                    <li>快速保存代码文件</li>
                    <li>如果页面出现异常，应该自动重载</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section success">
        <h2>预期结果</h2>
        <ul>
            <li><span class="status-indicator status-success"></span>正常情况下，检测到页面正常后停止检查</li>
            <li><span class="status-indicator status-warning"></span>异常情况下，自动重新加载页面</li>
            <li><span class="status-indicator status-info"></span>控制台显示详细的检测日志</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>调试信息</h2>
        <div id="debug-info">
            <p><strong>当前标题：</strong> <span id="current-title">-</span></p>
            <p><strong>当前路径：</strong> <span id="current-path">-</span></p>
            <p><strong>Vue组件数量：</strong> <span id="vue-components">-</span></p>
            <p><strong>页面内容：</strong> <span id="page-content">-</span></p>
        </div>
    </div>

    <script>
        let logCount = 0;
        
        function updateDebugInfo() {
            document.getElementById('current-title').textContent = document.title;
            document.getElementById('current-path').textContent = window.location.pathname;
            document.getElementById('vue-components').textContent = document.querySelectorAll('[data-v-]').length;
            document.getElementById('page-content').textContent = document.body.textContent.substring(0, 100) + '...';
        }
        
        function addLog(message, type = 'info') {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logOutput = document.getElementById('log-output');
            const logLine = `[${timestamp}] ${message}\n`;
            logOutput.textContent += logLine;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[自动重载测试] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDisplay = document.getElementById('status-display');
            const indicator = type === 'success' ? 'status-success' : 
                            type === 'warning' ? 'status-warning' : 
                            type === 'error' ? 'status-error' : 'status-info';
            
            statusDisplay.innerHTML = `<p><span class="status-indicator ${indicator}"></span>${message}</p>`;
        }
        
        function manualCheck() {
            const title = document.title;
            const bodyText = document.body.textContent || '';
            const currentPath = window.location.pathname;
            
            const hasHomeTitle = title.includes('Home - CaseX Management') || title.includes('Home - CaseX');
            const has404Content = bodyText.includes('404') || 
                                 bodyText.includes('Wrong address input') ||
                                 bodyText.includes('please re-enter the address');
            
            const isCorrectPath = currentPath === '/' || 
                                 currentPath === '/dashboard' || 
                                 currentPath === '/dashboard/ticket';
            
            const hasVueContent = document.querySelector('[data-v-]') ||
                                 document.querySelector('.layout-container') ||
                                 document.querySelector('.el-container');
            
            addLog(`手动检查结果: 标题=${hasHomeTitle}, 404内容=${has404Content}, 正确路径=${isCorrectPath}, Vue内容=${!!hasVueContent}`);
            
            if (hasHomeTitle && has404Content) {
                updateStatus('检测到异常：标题正确但显示404', 'error');
                addLog('检测到页面异常，建议重载', 'warning');
            } else if (isCorrectPath && !hasVueContent) {
                updateStatus('检测到异常：路径正确但无Vue内容', 'warning');
                addLog('检测到内容异常，可能需要重载', 'warning');
            } else {
                updateStatus('页面状态正常', 'success');
                addLog('页面状态正常', 'success');
            }
        }
        
        function simulateError() {
            // 修改页面标题和内容来模拟错误
            document.title = 'Home - CaseX Management';
            document.body.innerHTML = `
                <div style="text-align: center; padding: 50px;">
                    <h1>404</h1>
                    <p>Wrong address input, please re-enter the address~</p>
                    <button onclick="location.reload()">Back to home page</button>
                </div>
            `;
            
            addLog('已模拟404错误，页面应该在几秒后自动重载', 'warning');
            updateStatus('已模拟404错误，等待自动重载...', 'warning');
        }
        
        function clearLog() {
            document.getElementById('log-output').textContent = '';
            logCount = 0;
            addLog('日志已清除');
        }
        
        // 定期更新调试信息
        setInterval(updateDebugInfo, 2000);
        
        // 初始化
        updateDebugInfo();
        addLog('自动重载测试页面已加载');
        updateStatus('测试页面已就绪', 'success');
        
        // 监听页面重载事件
        window.addEventListener('beforeunload', function() {
            addLog('页面即将重载...');
        });
        
        // 检查是否有自动重载脚本在运行
        setTimeout(() => {
            if (typeof window.quickFix !== 'undefined') {
                addLog('检测到quickFix模块已加载', 'success');
            } else {
                addLog('未检测到quickFix模块', 'warning');
            }
        }, 1000);
    </script>
</body>
</html>
