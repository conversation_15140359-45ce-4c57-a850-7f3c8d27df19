import { Router } from 'vue-router';
import { Local, Session } from './storage';
import appSettings from '/@/config/index.js';
let timeoutId: ReturnType<typeof setTimeout>;

export function startTimer(router: Router) {
	const idleTimeout = appSettings.IDLE_TIMEOUT || 10; // 为 0 或空时 默认值 10
	// 设置定时器，如果用户在1小时内没有任何操作，就自动退出
	timeoutId = setInterval(
		() => {
			// 检查当前页面是否为登录页面
			if (router.currentRoute.value.name !== 'login') {
				const lastOperationTime = localStorage.getItem('lastOperationTime');
				if (new Date().getTime() - Number(lastOperationTime) > 60 * 60 * 1000) {
					Local.remove('userInfo');
					Local.remove('token');
					Local.remove('refreshToken');
					Local.remove('needToChangePwd');

					router.push('/login');
				}
			}
		},
		idleTimeout * 60 * 1000
	); // 10分钟检查一次
}

export function resetTimer() {
	//console.log(new Date().toLocaleTimeString() + ' 有操作...');
	// 当用户有操作时，重置定时器
	localStorage.setItem('lastOperationTime', String(new Date().getTime()));
}

export function setupAutoLogout(router: Router) {
	// 监听用户交互事件
	window.addEventListener('mousemove', resetTimer);
	window.addEventListener('mousedown', resetTimer);
	window.addEventListener('keypress', resetTimer);
	window.addEventListener('touchmove', resetTimer);

	// 开始计时
	startTimer(router);
}
