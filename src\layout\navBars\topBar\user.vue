<template>
	<div class="layout-navbars-breadcrumb-user pr15" :style="{ flex: layoutUserFlexNum }">
		<div class="rightLogo">
			<img v-if="isImageVisible" :src="state.info.logoInRigth" :width="state.info.logoInRigthWidth"
				:height="state.info.logoInRigthHeight" />
		</div>
		<el-dropdown :show-timeout="70" :hide-timeout="50" trigger="click" @command="onComponentSizeChange"
			v-if="!themeConfig.isShowBptsPage">
			<div class="layout-navbars-breadcrumb-user-icon">
				<i class="iconfont icon-ziti" :title="$t('message.user.title0')"></i>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="large" :disabled="state.disabledSize === 'large'">{{
						$t('message.user.dropdownLarge') }}</el-dropdown-item>
					<el-dropdown-item command="default" :disabled="state.disabledSize === 'default'">{{
						$t('message.user.dropdownDefault') }}</el-dropdown-item>
					<el-dropdown-item command="small" :disabled="state.disabledSize === 'small'">{{
						$t('message.user.dropdownSmall') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<el-dropdown :show-timeout="70" :hide-timeout="50" trigger="click" @command="onLanguageChange"
			v-if="!themeConfig.isShowBptsPage">
			<div class="layout-navbars-breadcrumb-user-icon">
				<i class="iconfont" :class="state.disabledI18n === 'en' ? 'icon-fuhao-yingwen' : 'icon-fuhao-zhongwen'"
					:title="$t('message.user.title1')"></i>
			</div>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="zh-cn" :disabled="state.disabledI18n === 'zh-cn'">简体中文</el-dropdown-item>
					<el-dropdown-item command="en" :disabled="state.disabledI18n === 'en'">English</el-dropdown-item>
					<el-dropdown-item command="zh-tw" :disabled="state.disabledI18n === 'zh-tw'">繁體中文</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onSearchClick" v-if="!themeConfig.isShowBptsPage">
			<el-icon :title="$t('message.user.title2')">
				<ele-Search />
			</el-icon>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onLayoutSetingClick"
			v-if="!themeConfig.isShowBptsPage">
			<i class="icon-skin iconfont" :title="$t('message.user.title3')"></i>
		</div>

		<div class="layout-navbars-breadcrumb-user-icon mr10" @click="onScreenfullClick"
			v-if="!themeConfig.isShowBptsPage">
			<i class="iconfont" :title="state.isScreenfull ? $t('message.user.title6') : $t('message.user.title5')"
				:class="!state.isScreenfull ? 'icon-fullscreen' : 'icon-tuichuquanping'"></i>
		</div>
		<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
			<span class="layout-navbars-breadcrumb-user-link">
				<img :src="userInfos.photo" class="layout-navbars-breadcrumb-user-link-photo mr5"
					v-if="!themeConfig.isShowBptsPage" />
				{{ $t('message.user.welcome') }} {{ userInfos.userName === '' ? 'common' : userInfos.userName }}
				<el-icon class="el-icon--right">
					<ele-ArrowDown />
				</el-icon>
			</span>
			<template #dropdown>
				<el-dropdown-menu v-if="!themeConfig.isShowBptsPage">
					<el-dropdown-item command="/home">{{ $t('message.user.dropdown1') }}</el-dropdown-item>
					<el-dropdown-item command="wareHouse">{{ $t('message.user.dropdown6') }}</el-dropdown-item>
					<el-dropdown-item command="/personal">{{ $t('message.user.dropdown2') }}</el-dropdown-item>
					<el-dropdown-item command="/404">{{ $t('message.user.dropdown3') }}</el-dropdown-item>
					<el-dropdown-item command="/401">{{ $t('message.user.dropdown4') }}</el-dropdown-item>
					<el-dropdown-item divided command="logOut">{{ $t('message.user.dropdown5') }}</el-dropdown-item>
				</el-dropdown-menu>

				<el-dropdown-menu v-if="themeConfig.isShowBptsPage">
					<el-dropdown-item command="/personal">{{ $t('message.user.dropdown2') }}</el-dropdown-item>
					<el-dropdown-item divided command="logOut">{{ $t('message.user.dropdown5') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<div class="layout-navbars-breadcrumb-user-icon" ref="userNewsBadgeRef" v-click-outside="onUserNewsClick">
			<el-badge :is-dot="state.showMsg">
				<el-icon :title="$t('message.user.title4')">
					<ele-Bell />
				</el-icon>
			</el-badge>
		</div>
		<el-popover ref="userNewsRef" :virtual-ref="userNewsBadgeRef" placement="bottom" trigger="click" :popper-style="{ maxHeight: '400px', overflowY: 'auto' }"
			transition="el-zoom-in-top" virtual-triggering :width="300" :persistent="false">
			<UserNews />
		</el-popover>

		<div @click="onHelpClick" style="margin-left: 20px; margin-right: 20px; font-size: 12px; ">
			<el-link target="_blank" type="primary">Help</el-link>
		</div>
		<Search ref="searchRef" />
	</div>
</template>

<script setup lang="ts" name="layoutBreadcrumbUser">
import { defineAsyncComponent, ref, unref, computed, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage, ClickOutside as vClickOutside } from 'element-plus';
import screenfull from 'screenfull';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import mittBus from '/@/utils/mitt';
import { Session, Local } from '/@/utils/storage';
import loginApi from '/@/api/login';
import configApi from '/@/api/config/index';
import { Siteinfo } from '/@/types/siteConfig';
import cmTicketContentApi from '/@/api/cmTicketContents/index';

// 引入组件
const UserNews = defineAsyncComponent(() => import('/@/layout/navBars/topBar/userNews.vue'));
const Search = defineAsyncComponent(() => import('/@/layout/navBars/topBar/search.vue'));

// 定义变量内容
const userNewsRef = ref();
const userNewsBadgeRef = ref();
const { locale, t } = useI18n();
const router = useRouter();
const stores = useUserInfo();
const storesThemeConfig = useThemeConfig();
const { userInfos } = storeToRefs(stores);
const { themeConfig } = storeToRefs(storesThemeConfig);
const searchRef = ref();
const state = reactive({
	isScreenfull: false,
	disabledI18n: 'zh-cn',
	disabledSize: 'large',
	info: {} as Siteinfo,
	showMsg: false,
});

// 设置分割样式
const layoutUserFlexNum = computed(() => {
	let num: string | number = '';
	const { layout, isClassicSplitMenu } = themeConfig.value;
	const layoutArr: string[] = ['defaults', 'columns'];
	if (layoutArr.includes(layout) || (layout === 'classic' && !isClassicSplitMenu)) num = '1';
	else num = '';
	return num;
});
// 全屏点击时
const onScreenfullClick = () => {
	if (!screenfull.isEnabled) {
		ElMessage.warning('暂不不支持全屏');
		return false;
	}
	screenfull.toggle();
	screenfull.on('change', () => {
		if (screenfull.isFullscreen) state.isScreenfull = true;
		else state.isScreenfull = false;
	});
};
// 消息通知点击时
const onUserNewsClick = () => {
	unref(userNewsRef).popperRef?.delayHide?.();
};

// 布局配置 icon 点击时
const onLayoutSetingClick = () => {
	mittBus.emit('openSetingsDrawer');
};

const SsoLogout = async (bptsSSOLogin: any, userId: any) => {
	await loginApi.SSOLogout({ bptsSSOLogin: bptsSSOLogin, userId: userId })
		.then((rs) => {
			window.location.href = rs.data.data
		});
}

// 下拉菜单点击时
const onHandleCommandClick = async (path: string) => {
	if (path === 'logOut') {

		if (userInfos.value.ssoLogin) {
			await SsoLogout(userInfos.value.bptsSSOLogin, userInfos.value.userId);
		} else {
			loginApi.SignOut({ token: Local.get('token'), userName: userInfos.value.userName }).then((rs) => {
				Session.clear(); // 清除缓存/token等
				Local.clearToken();
				// window.location.reload();
				router.push('/login');
			});
		}

		// ElMessageBox({
		// 	closeOnClickModal: false,
		// 	closeOnPressEscape: false,
		// 	title: t('message.user.logOutTitle'),
		// 	message: t('message.user.logOutMessage'),
		// 	showCancelButton: true,
		// 	confirmButtonText: t('message.user.logOutConfirm'),
		// 	cancelButtonText: t('message.user.logOutCancel'),
		// 	buttonSize: 'default',
		// 	beforeClose: (action, instance, done) => {
		// 		if (action === 'confirm') {
		// 			instance.confirmButtonLoading = true;
		// 			instance.confirmButtonText = t('message.user.logOutExit');
		// 			setTimeout(() => {
		// 				done();
		// 				setTimeout(() => {
		// 					instance.confirmButtonLoading = false;
		// 				}, 300);
		// 			}, 700);
		// 		} else {
		// 			done();
		// 		}
		// 	},
		// })
		// 	.then(async () => {
		// 		// 清除缓存/token等
		// 		Session.clear();
		// 		// 使用 reload 时，不需要调用 resetRoute() 重置路由
		// 		window.location.reload();
		// 	})
		// 	.catch(() => {});
	} else if (path === 'wareHouse') {
		window.open('https://gitee.com/lyt-top/vue-next-admin');
	} else {
		router.push(path);
	}
};
// 菜单搜索点击
const onSearchClick = () => {
	searchRef.value.openSearch();
};
// 组件大小改变
const onComponentSizeChange = (size: string) => {
	Local.remove('themeConfig');
	themeConfig.value.globalComponentSize = size;
	Local.set('themeConfig', themeConfig.value);
	initI18nOrSize('globalComponentSize', 'disabledSize');
	window.location.reload();
};
// 语言切换
const onLanguageChange = (lang: string) => {
	Local.remove('themeConfig');
	themeConfig.value.globalI18n = lang;
	Local.set('themeConfig', themeConfig.value);
	locale.value = lang;
	other.useTitle();
	initI18nOrSize('globalI18n', 'disabledI18n');
};
// 初始化组件大小/i18n
const initI18nOrSize = (value: string, attr: string) => {
	(<any>state)[attr] = Local.get('themeConfig')[value];
};

const onHelpClick = () => {
	router.push('/help')
};
// 页面加载时
onMounted(() => {
	if (Local.get('themeConfig')) {
		initI18nOrSize('globalComponentSize', 'disabledSize');
		initI18nOrSize('globalI18n', 'disabledI18n');
	}

	GetTicketContentReadMsg();

	configApi.GetAppConfig().then((rs) => {
		state.info = rs.data;
		loadImage(state.info.logoInRigth).then(img => {
			isImageVisible.value = true
		}).catch(err => {
			isImageVisible.value = false
		});
	});

	//事件监听器
	mittBus.on<any>('SendTiketNum', (row: any) => {
		console.log("bbbb:",row);
		GetTicketContentReadMsg();
	});
});

const GetTicketContentReadMsg = () => {
    let info={
			createdById: userInfos.value.userId ,
			assginToNodeId: userInfos.value.nodeId,
	};
	cmTicketContentApi
		.IsReadMsg(info)
		.then((rs) => {

			state.showMsg = rs.data;
		})
		.catch((rs) => { })
		.finally();
}

const isImageVisible = ref(true)


const loadImage = (src: any) => {
	return new Promise((resolve, reject) => {
		let img = new Image();
		img.onload = () => resolve(img);
		img.onerror = () => reject(new Error('Image load failed'));
		img.src = src;
	});
}
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;

	&-link {
		height: 100%;
		display: flex;
		align-items: center;
		white-space: nowrap;

		&-photo {
			width: 25px;
			height: 25px;
			border-radius: 100%;
		}
	}

	&-icon {
		padding: 0 10px;
		cursor: pointer;
		color: var(--next-bg-topBarColor);
		height: 50px;
		line-height: 50px;
		display: flex;
		align-items: center;

		&:hover {
			background: var(--next-color-user-hover);

			i {
				display: inline-block;
				animation: logoAnimation 0.3s ease-in-out;
			}
		}
	}

	:deep(.el-dropdown) {
		color: var(--next-bg-topBarColor);
	}

	:deep(.el-badge) {
		height: 40px;
		line-height: 40px;
		display: flex;
		align-items: center;
	}

	:deep(.el-badge__content.is-fixed) {
		top: 12px;
	}

	.rightLogo {
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: rgb(0 21 41 / 2%) 0px 1px 4px;
		color: var(--el-color-primary);
		font-size: 20px;
		font-weight: bold;
		cursor: pointer;
		animation: logoAnimation 0.3s ease-in-out;
		padding-right: 20px;

		&:hover {
			span {
				color: var(--color-primary-light-2);
			}
		}

		&-medium-img {
			width: 20px;
			margin-right: 5px;
		}
	}
}
</style>
