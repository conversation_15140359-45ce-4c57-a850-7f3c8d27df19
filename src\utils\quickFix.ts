/**
 * 快速修复工具 - 检测页面异常并自动重载
 * 专门解决 "Home - CaseX Management" 标题但显示404的问题
 */

let checkCount = 0;
const MAX_CHECKS = 10; // 最大检查次数
const CHECK_INTERVAL = 2000; // 检查间隔2秒

/**
 * 检测页面是否需要重载
 */
function shouldReload(): boolean {
    const title = document.title;
    const bodyText = document.body.textContent || '';
    const currentPath = window.location.pathname;
    
    // 条件1: 标题包含 "Home - CaseX Management" 但页面显示404
    const hasCorrectTitle = title.includes('Home - CaseX Management') || title.includes('Home - CaseX');
    const has404Error = bodyText.includes('404') || 
                       bodyText.includes('Wrong address input') ||
                       bodyText.includes('please re-enter the address');
    
    // 条件2: 在正确路径但没有正常内容
    const isCorrectPath = currentPath === '/' || 
                         currentPath === '/dashboard' || 
                         currentPath === '/dashboard/ticket';
    
    const hasNormalContent = document.querySelector('.layout-container') ||
                            document.querySelector('.el-container') ||
                            document.querySelector('.home-container') ||
                            document.querySelector('[class*="layout"]') ||
                            (document.querySelectorAll('[data-v-]').length > 5); // Vue组件
    
    console.log('页面状态检查:', {
        title,
        currentPath,
        hasCorrectTitle,
        has404Error,
        hasNormalContent,
        checkCount
    });
    
    // 如果标题正确但显示404，或者在正确路径但没有正常内容
    if ((hasCorrectTitle && has404Error) || 
        (isCorrectPath && !hasNormalContent && !has404Error)) {
        return true;
    }
    
    return false;
}

/**
 * 执行页面重载
 */
function performReload(): void {
    console.log('🔄 检测到页面异常，正在重新加载...');
    
    // 显示加载提示（如果可能）
    try {
        const loadingDiv = document.createElement('div');
        loadingDiv.innerHTML = `
            <div style="
                position: fixed; 
                top: 50%; 
                left: 50%; 
                transform: translate(-50%, -50%); 
                background: rgba(0,0,0,0.8); 
                color: white; 
                padding: 20px; 
                border-radius: 8px; 
                z-index: 9999;
                font-family: Arial, sans-serif;
            ">
                🔄 页面异常，正在重新加载...
            </div>
        `;
        document.body.appendChild(loadingDiv);
    } catch (e) {
        // 忽略错误
    }
    
    // 延迟重载，给用户看到提示
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

/**
 * 开始检查页面状态
 */
function startChecking(): void {
    const checkTimer = setInterval(() => {
        checkCount++;
        
        try {
            if (shouldReload()) {
                clearInterval(checkTimer);
                performReload();
                return;
            }
            
            // 如果检查次数超过限制，停止检查
            if (checkCount >= MAX_CHECKS) {
                clearInterval(checkTimer);
                console.log('页面状态检查完成，未发现异常');
                return;
            }
            
            // 如果页面已经正常加载，停止检查
            const hasNormalContent = document.querySelector('.layout-container') ||
                                   document.querySelector('.el-container') ||
                                   document.querySelector('.home-container');
            
            if (hasNormalContent) {
                clearInterval(checkTimer);
                console.log('✅ 页面正常加载，停止检查');
                return;
            }
            
        } catch (error) {
            console.error('页面状态检查出错:', error);
        }
    }, CHECK_INTERVAL);
}

/**
 * 初始化快速修复
 */
function initQuickFix(): void {
    // 等待页面基本加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(startChecking, 3000); // 3秒后开始检查
        });
    } else {
        setTimeout(startChecking, 3000); // 3秒后开始检查
    }
}

// 立即执行初始化
initQuickFix();

// 导出到全局对象，方便调试
if (typeof window !== 'undefined') {
    (window as any).quickFix = {
        check: shouldReload,
        reload: performReload,
        getCheckCount: () => checkCount
    };
}

export { shouldReload, performReload };
