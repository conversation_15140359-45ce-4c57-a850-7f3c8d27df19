<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta name="keywords" content="" />
	<meta name="description" content="CaseX Management" />

	<!-- <meta http-equiv="pragram" content="no-cache" />
		<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="expires" content="0" /> -->

	<link rel="icon" href="/favicon.ico?v=1.0.1" />
	<title>CaseX Management</title>
	<script type="text/javascript">
		document.write("<script src='config.js?" + new Date().getTime() + "'><\/script>");
	</script>
</head>

<body>
	<div id="app"></div>
	<script type="module" src="/src/main.ts"></script>

	<!-- 修复：无缝标题监控和自动重载 -->
	<script>
		(function() {
			let reloadTriggered = false;

			// 检测异常标题
			function isAbnormalTitle(title) {
				// 异常情况1：显示路径作为标题
				if (title.includes('/ - CaseX Management') ||
					title.includes('/dashboard - CaseX Management')) {
					return true;
				}

				// 异常情况2：标题正确但页面有404内容
				if (title.includes('Home - CaseX Management')) {
					const bodyText = document.body.textContent || '';
					if (bodyText.includes('404') || bodyText.includes('Wrong address input')) {
						return true;
					}
				}

				return false;
			}

			// 立即重载
			function instantReload(reason) {
				if (reloadTriggered) return;
				reloadTriggered = true;
				console.log('🔄 ' + reason + ' - 立即重载');
				window.location.reload();
			}

			// 标题监控
			function monitorTitle() {
				const title = document.title;
				if (isAbnormalTitle(title)) {
					instantReload('检测到异常标题: ' + title);
				}
			}

			// 启动监控
			function startMonitoring() {
				// 1. 监听标题元素变化
				const titleElement = document.querySelector('title');
				if (titleElement) {
					const observer = new MutationObserver(monitorTitle);
					observer.observe(titleElement, { childList: true, subtree: true });
				}

				// 2. 高频检查（每200ms）
				const interval = setInterval(function() {
					if (reloadTriggered) {
						clearInterval(interval);
						return;
					}
					monitorTitle();
				}, 200);

				// 3. 初始检查
				setTimeout(monitorTitle, 500);

				console.log('✅ 标题监控已启动');
			}

			// 立即启动
			if (document.readyState === 'loading') {
				document.addEventListener('DOMContentLoaded', startMonitoring);
			} else {
				startMonitoring();
			}
		})();
	</script>
	
</body>

</html>