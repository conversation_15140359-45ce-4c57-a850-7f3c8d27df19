<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta name="keywords" content="" />
	<meta name="description" content="CaseX Management" />

	<!-- <meta http-equiv="pragram" content="no-cache" />
		<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="expires" content="0" /> -->

	<link rel="icon" href="/favicon.ico?v=1.0.1" />
	<title>CaseX Management</title>
	<script type="text/javascript">
		document.write("<script src='config.js?" + new Date().getTime() + "'><\/script>");
	</script>
</head>

<body>
	<div id="app"></div>
	<script type="module" src="/src/main.ts"></script>

	<!-- 修复：页面异常检测和自动重载脚本 -->
	<script>
		(function() {
			let checkCount = 0;
			const MAX_CHECKS = 8;

			function checkPageStatus() {
				checkCount++;

				try {
					const title = document.title;
					const bodyText = document.body.textContent || '';
					const currentPath = window.location.pathname;

					// 检测条件：标题包含Home但页面显示404
					const hasHomeTitle = title.includes('Home - CaseX Management') || title.includes('Home - CaseX');
					const has404Content = bodyText.includes('404') ||
										 bodyText.includes('Wrong address input') ||
										 bodyText.includes('please re-enter the address');

					// 检测条件：在正确路径但没有Vue应用内容
					const isCorrectPath = currentPath === '/' ||
										 currentPath === '/dashboard' ||
										 currentPath === '/dashboard/ticket';

					const hasVueContent = document.querySelector('[data-v-]') ||
										 document.querySelector('.layout-container') ||
										 document.querySelector('.el-container');

					console.log('页面状态检测 #' + checkCount + ':', {
						title: title,
						path: currentPath,
						hasHomeTitle: hasHomeTitle,
						has404Content: has404Content,
						hasVueContent: !!hasVueContent
					});

					// 如果标题正确但显示404，立即重载
					if (hasHomeTitle && has404Content) {
						console.log('🔄 检测到页面异常（标题正确但显示404），正在重新加载...');
						window.location.reload();
						return;
					}

					// 如果在正确路径但没有Vue内容，且检查次数超过5次，重载
					if (isCorrectPath && !hasVueContent && checkCount > 5) {
						console.log('🔄 检测到页面异常（路径正确但无内容），正在重新加载...');
						window.location.reload();
						return;
					}

					// 如果有正常内容，停止检查
					if (hasVueContent && !has404Content) {
						console.log('✅ 页面正常，停止检查');
						return;
					}

					// 继续检查
					if (checkCount < MAX_CHECKS) {
						setTimeout(checkPageStatus, 3000);
					} else {
						console.log('页面检查完成，未发现异常');
					}

				} catch (error) {
					console.error('页面状态检查出错:', error);
				}
			}

			// 页面加载完成后开始检查
			if (document.readyState === 'loading') {
				document.addEventListener('DOMContentLoaded', function() {
					setTimeout(checkPageStatus, 5000); // 5秒后开始检查
				});
			} else {
				setTimeout(checkPageStatus, 5000); // 5秒后开始检查
			}
		})();
	</script>
	
</body>

</html>