<template>
	<div class="send-email-container">
		<el-card shadow="never" class="mb10" style="height: calc(100vh - 200px); overflow-y: auto;">
			<template #header>
				<div class="card-header">
					<span>{{ $t('message.emailCommon.emailInformation') }}</span>
					<div>
						<el-button v-if="emailId == 0 || ruleForm.status == 0" :loading="saveDraftloading"
							type="primary" @click="saveForm(0)" size="small">{{
								$t('message.page.buttonSave')
							}}</el-button>
						<el-button v-if="emailId == 0 || ruleForm.status == 0" :loading="sendloading" type="primary"
							@click="saveForm(1)" size="small">{{
								$t('message.page.buttonSend')
							}}</el-button>
						<el-button v-if="ruleForm.status > 1" @click="onReSend" type="danger" size="small">{{
							$t('message.page.reSend') }}</el-button>
						<el-button @click="onBack" size="small">{{ $t('message.page.cannel') }}</el-button>
					</div>
				</div>
			</template>
			<el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="150px" class="mt10" height="100vh">
				<el-row :gutter="35">
					<el-col v-if="emailId > 0" :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.page.createAt')">
							<span>{{ ruleForm.createdAt }}</span>
						</el-form-item>
					</el-col>

					<el-col v-if="emailId > 0" :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.sendDate')">
							<span>{{ ruleForm.sendData }}</span>
						</el-form-item>
					</el-col>

					<el-col v-if="emailId > 0" :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailStatus')">
							<el-select v-model="ruleForm.status" clearable class="w100">
								<el-option label="Draft" :value="0"></el-option>
								<el-option label="Sending" :value="1"></el-option>
								<el-option label="Sent" :value="2"></el-option>
								<el-option label="Failed" :value="3"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col v-if="emailId > 0" :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.router.emailTemplate')" v-if="1 == 2">
							<el-select v-model="ruleForm.templateId" placeholder="Select" clearable class="w100">
								<el-option v-for="item in tempData" :label="item.name"
									:value="item.templateId"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailSubject')" prop="subject">
							<el-input v-model="ruleForm.subject" />
						</el-form-item>
					</el-col>

					<el-col v-if="emailId > 0" :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailFrom')" prop="fromAddr">
							<el-input v-model="ruleForm.fromAddr" :disabled="true" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailTo')">
							<mail-input :list="ruleForm.addrObjList"></mail-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailCc')">
							<mail-input :list="ruleForm.ccAddrObjList"></mail-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.attachment')">
							<div class="upload-div">
								<sc-upload-file v-model="fileurlArr" :action="actionStr" :limit="100" name="files">
									<el-button type="primary" style="width: 150px" icon="ele-Upload" size="small">{{
										$t('message.page.uploadFile') }}</el-button>
								</sc-upload-file>
							</div>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb6">
						<el-form-item :label="$t('message.emailFields.emailBody')" prop="bodyHtml">
							<Editor v-model="ruleForm.bodyHtml" class="ed" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, reactive, computed, onMounted, getCurrentInstance, defineAsyncComponent, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// import { useStore } from '/@/store/index';
import { getElcascaderSingle, isNotEmptyOrNull, parseThanZero } from '/@/utils';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatStrDate } from '/@/utils/formatTime';
import emailTemplateApi from '/@/api/emailTemplate/index';
import emailsApi from '/@/api/emails/index';
import { useI18n } from 'vue-i18n';
import MailInput from './mailInput.vue';
import mittBus from '/@/utils/mitt';

export default defineComponent({
	name: 'SendEmail',
	components: {
		// Editor: defineAsyncComponent(() => import('/@/components/editor/index.vue')),
		Editor: defineAsyncComponent(() => import('/@/components/editor/indexV5.vue')),
		MailInput,
	},
	setup() {
		const { t } = useI18n();
		const route = useRoute();
		const router = useRouter();
		// const store = useStore();
		const { proxy } = getCurrentInstance() as any;
		const BASE_URL = computed(() => {
			const appSettings = proxy?.$appSettings;
			return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
		});

		const state = reactive({
			saveDraftloading: false,
			sendloading: false,
			emailId: 0,
			ruleForm: { status: 1, fromAddr: '<EMAIL>', dataType: 'SendEmail', addrObjList: [], ccAddrObjList: [] },
			rules: {
				templateId: [{ required: true, message: 'Please input', trigger: 'blur' }],
				subject: [{ required: true, message: 'Please input', trigger: 'blur' }],
				bodyHtml: [{ required: true, message: 'Please input', trigger: 'blur' }],
				fromAddr: [{ required: true, message: 'Please input', trigger: 'blur' }],
				toAddrList: [{ required: true, message: 'Please input', trigger: 'change' }],
				ccAddrList: [{ required: true, message: 'Please input', trigger: 'change' }],
				status: [{ required: true, message: 'Please input', trigger: 'blur' }],
			},
			tempData: [],
			allAddrData: [],
			fileurlArr: [],
			//actionStr: `${BASE_URL}Api/Files/UpLoad`,
			actionStr: `${BASE_URL.value}Api/Files/UpLoadV2`,
		});

		// 获取用户信息 vuex
		// const currentUser = computed(() => {
		// 	return store.state.userInfos.userInfos;
		// });

		const onBack = () => {
			mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
			//router.push('/system/organization');
		};

		const onReSend = () => {
			state.ruleForm.toAddrList = [];
			state.ruleForm.ccAddrList = [];

			const hasFaild = ref(false);

			state.ruleForm.addrObjList.forEach((item) => {
				if (item.status == 'err') {
					hasFaild.value = true;
				} else {
					state.ruleForm.toAddrList.push(item.email);
				}
			});

			state.ruleForm.ccAddrObjList.forEach((item) => {
				if (item.status == 'err') {
					hasFaild.value = true;
				} else {
					state.ruleForm.ccAddrList.push(item.email);
				}
			});

			if (hasFaild.value) {
				proxy.$message('Incorret email format', 'error');
				return;
			}

			ElMessageBox.confirm(t('message.page.dlgReSendText'), t('message.page.dlgTip'), {
				confirmButtonText: t('message.page.confirm'),
				cancelButtonText: t('message.page.cannel'),
				type: 'warning',
				closeOnClickModal: false,
			}).then(() => {
				saveForm(1);
			});
		};

		// 新增
		const saveForm = (status: number) => {
			state.ruleForm.toAddrList = [];
			state.ruleForm.ccAddrList = [];

			const hasFaild = ref(false);

			state.ruleForm.addrObjList.forEach((item) => {
				if (item.status == 'err') {
					hasFaild.value = true;
				} else {
					state.ruleForm.toAddrList.push(item.email);
				}
			});

			state.ruleForm.ccAddrObjList.forEach((item) => {
				if (item.status == 'err') {
					hasFaild.value = true;
				} else {
					state.ruleForm.ccAddrList.push(item.email);
				}
			});

			if (hasFaild.value) {
				proxy.$message('Incorret email format', 'error');
				return;
			}

			proxy.$refs.ruleFormRef.validate((valid) => {
				if (!valid) {
					return;
				}
				var obj = Object.assign({}, state.ruleForm);

				obj.status = status;

				//获取文件的id
				if (state.fileurlArr.length > 0) {
					obj.fileList = state.fileurlArr.map((a) => {
						return a.id;
					});
				}

				if (status == 0) {
					state.saveDraftloading = true;
				}

				if (status == 1) {
					state.sendloading = true;
				}

				emailsApi
					.SendEmail(obj)
					.then((rs) => {
						ElMessage.success(t('message.emailStatus.sended'));
						// router.push('/emails/list');

						let closePath = '/emails/sendEmail';
						if (isNotEmptyOrNull(route.params.id)) {
							closePath = '/emails/sendEmail/:id';
						}

						// mittBus.emit('onCurrentContextmenuClick', {
						// 	contextMenuClickId: 1,
						// 	path: closePath,
						// 	meta: {},
						// 	name: '',
						// });

						mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
					})
					.catch((rs) => {
						//ElMessage.error(rs.resultMsg);
					})
					.finally(() => {
						if (status == 0) {
							state.saveDraftloading = false;
						}
						if (status == 1) {
							state.sendloading = false;
						}
					});
			});
		};

		// 页面加载时
		onMounted(async () => {
			if (parseThanZero(route.params.id)) {
				state.emailId = route.params.id;
				await emailsApi.Detail(route.params.id).then((rs) => {
					var item = rs.data;
					if (item.attachmentList) {
						item.fileList = item.attachmentList.map((a) => {
							return a.id;
						});
					}

					if (item.toAddrs) {
						item.toAddrs.split(';').forEach((item) => {
							state.ruleForm.addrObjList.push({
								content: item.split('@')[0] + ' <' + item + '>',
								name: item.split('@')[0],
								email: item,
								status: 'normal',
							});
						});
					}

					if (item.ccAddrs) {
						item.ccAddrs.split(';').forEach((item) => {
							state.ruleForm.ccAddrObjList.push({
								content: item.split('@')[0] + ' <' + item + '>',
								name: item.split('@')[0],
								email: item,
								status: 'normal',
							});
						});
					}

					item.addrObjList = state.ruleForm.addrObjList;
					item.ccAddrObjList = state.ruleForm.ccAddrObjList;
					state.ruleForm = item;
				});
			}

			emailsApi.GetEmails().then((rs) => {
				state.allAddrData = rs.data;
			});

			emailTemplateApi.GetList({}).then((rs) => {
				state.tempData = rs.data;
			});
		});

		return {
			saveForm,
			onBack,
			onReSend,
			formatStrDate,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
.send-email-container {
	padding: 10px;
	background-color: #ffffff;

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.upload-div {
		width: 300px;
	}

	.ed {
		width: 100%;
	}
}

.el-card ::v-deep .el-card__header {
	padding: 5px 10px;
}
</style>
